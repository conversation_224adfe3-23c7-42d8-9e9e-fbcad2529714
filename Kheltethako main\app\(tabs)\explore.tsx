import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Search, MapPin, Star, Filter, X } from 'lucide-react-native';
import { searchVenues } from '@/services/supabaseApi';
import AdvancedSearch, { SearchFilters } from '@/components/AdvancedSearch';

const FILTERS = [
  { id: 'cricket', name: 'Cricket' },
  { id: 'football', name: 'Football' },
  { id: 'basketball', name: 'Basketball' },
  { id: 'badminton', name: '<PERSON><PERSON><PERSON>' },
  { id: 'swimming', name: 'Swimming' },
  { id: 'indoor', name: 'Indoor' },
  { id: 'outdoor', name: 'Outdoor' },
  { id: 'ac', name: 'Air Conditioned' },
];

export default function ExploreScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [venues, setVenues] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [sortBy, setSortBy] = useState('recommended'); // 'recommended', 'price_low', 'price_high', 'rating'
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState<SearchFilters>({
    query: '',
    sports: [],
    location: '',
    priceRange: [0, 10000],
    availability: 'any',
    amenities: [],
    rating: 0,
    sortBy: 'relevance',
  });

  useEffect(() => {
    fetchVenues();
  }, [selectedFilters, sortBy]);

  const fetchVenues = async () => {
    try {
      setIsLoading(true);
      const data = await searchVenues(searchQuery, selectedFilters, sortBy);
      setVenues(data);
    } catch (error) {
      console.error('Error fetching venues:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    fetchVenues();
  };

  const toggleFilter = (filterId) => {
    if (selectedFilters.includes(filterId)) {
      setSelectedFilters(selectedFilters.filter((id) => id !== filterId));
    } else {
      setSelectedFilters([...selectedFilters, filterId]);
    }
  };

  const clearFilters = () => {
    setSelectedFilters([]);
  };

  const handleAdvancedSearch = (filters: SearchFilters) => {
    setAdvancedFilters(filters);
    setSearchQuery(filters.query);
    setSortBy(filters.sortBy);
    // Convert advanced filters to simple filters for compatibility
    setSelectedFilters(filters.sports);
    fetchVenuesWithAdvancedFilters(filters);
  };

  const fetchVenuesWithAdvancedFilters = async (filters: SearchFilters) => {
    try {
      setIsLoading(true);
      // Use advanced search with all filters
      const data = await searchVenues(filters.query, filters.sports, filters.sortBy);
      // In a real implementation, you would pass all filters to the API
      setVenues(data);
    } catch (error) {
      console.error('Error fetching venues with advanced filters:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.title}>Explore Venues</Text>
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowAdvancedSearch(true)}
        >
          <Filter size={20} color="#111827" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={18} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search venues, sports, locations..."
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
            onSubmitEditing={handleSearch}
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => {
                setSearchQuery('');
                fetchVenues();
              }}
              style={styles.clearButton}
            >
              <X size={18} color="#6B7280" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {selectedFilters.length > 0 && (
        <View style={styles.activeFiltersContainer}>
          <FlatList
            data={selectedFilters}
            horizontal
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => {
              const filter = FILTERS.find((f) => f.id === item);
              return (
                <TouchableOpacity
                  style={styles.activeFilterItem}
                  onPress={() => toggleFilter(item)}
                >
                  <Text style={styles.activeFilterText}>{filter?.name}</Text>
                  <X size={16} color="#FFFFFF" />
                </TouchableOpacity>
              );
            }}
            keyExtractor={(item) => item}
            ListHeaderComponent={
              <TouchableOpacity
                style={styles.clearFiltersButton}
                onPress={clearFilters}
              >
                <Text style={styles.clearFiltersText}>Clear All</Text>
              </TouchableOpacity>
            }
          />
        </View>
      )}

      <View style={styles.filterTagsContainer}>
        <FlatList
          data={FILTERS}
          horizontal
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.filterTag,
                selectedFilters.includes(item.id) && styles.filterTagSelected,
              ]}
              onPress={() => toggleFilter(item.id)}
            >
              <Text
                style={[
                  styles.filterTagText,
                  selectedFilters.includes(item.id) &&
                    styles.filterTagTextSelected,
                ]}
              >
                {item.name}
              </Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
        />
      </View>

      <View style={styles.sortContainer}>
        <Text style={styles.resultsText}>
          {venues.length} {venues.length === 1 ? 'venue' : 'venues'} found
        </Text>
        <View style={styles.sortButtonsContainer}>
          <Text style={styles.sortByText}>Sort by:</Text>
          <TouchableOpacity
            style={[
              styles.sortButton,
              sortBy === 'recommended' && styles.sortButtonActive,
            ]}
            onPress={() => setSortBy('recommended')}
          >
            <Text
              style={[
                styles.sortButtonText,
                sortBy === 'recommended' && styles.sortButtonTextActive,
              ]}
            >
              Recommended
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.sortButton,
              sortBy === 'price_low' && styles.sortButtonActive,
            ]}
            onPress={() => setSortBy('price_low')}
          >
            <Text
              style={[
                styles.sortButtonText,
                sortBy === 'price_low' && styles.sortButtonTextActive,
              ]}
            >
              Price: Low to High
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {isLoading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0B8457" />
        </View>
      ) : (
        <FlatList
          data={venues}
          contentContainerStyle={styles.venuesList}
          keyExtractor={(item) => item.id.toString()}
          numColumns={2}
          columnWrapperStyle={styles.row}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.venueCard}
              onPress={() => router.push(`/venue/${item.id}`)}
            >
              <Image
                source={{ uri: item.image_url }}
                style={styles.venueImage}
              />
              {item.is_featured && (
                <View style={styles.featuredTag}>
                  <Text style={styles.featuredTagText}>Featured</Text>
                </View>
              )}
              <View style={styles.venueDetails}>
                <Text style={styles.venueName} numberOfLines={2}>{item.name}</Text>
                <View style={styles.venueLocation}>
                  <MapPin size={12} color="#6B7280" />
                  <Text style={styles.venueLocationText} numberOfLines={1}>
                    {item.location}
                  </Text>
                </View>
                <View style={styles.venueFeatures}>
                  {item.features?.slice(0, 2).map((feature, index) => (
                    <View key={index} style={styles.featureTag}>
                      <Text style={styles.featureTagText}>{feature}</Text>
                    </View>
                  ))}
                  {item.features?.length > 2 && (
                    <View style={styles.featureTag}>
                      <Text style={styles.featureTagText}>+{item.features.length - 2}</Text>
                    </View>
                  )}
                </View>
                <View style={styles.venueFooter}>
                  <View style={styles.venueRating}>
                    <Star size={12} color="#F59E0B" fill="#F59E0B" />
                    <Text style={styles.venueRatingText}>
                      {item.rating}
                    </Text>
                  </View>
                  <Text style={styles.venuePrice}>
                    ৳{item.price_per_hour}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          )}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateTitle}>No Venues Found</Text>
              <Text style={styles.emptyStateText}>
                Try adjusting your filters or search for something else
              </Text>
            </View>
          }
        />
      )}

      <AdvancedSearch
        visible={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onApplyFilters={handleAdvancedSearch}
        initialFilters={advancedFilters}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 46,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontFamily: 'Inter-Regular',
    fontSize: 16,
    color: '#111827',
  },
  clearButton: {
    padding: 4,
  },
  filterTagsContainer: {
    paddingLeft: 20,
    marginBottom: 16,
  },
  filterTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    marginRight: 8,
  },
  filterTagSelected: {
    backgroundColor: '#0B8457',
  },
  filterTagText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#4B5563',
  },
  filterTagTextSelected: {
    color: '#FFFFFF',
  },
  activeFiltersContainer: {
    marginBottom: 12,
    paddingLeft: 20,
  },
  activeFilterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0B8457',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
  },
  activeFilterText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginRight: 4,
  },
  clearFiltersButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  clearFiltersText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#4B5563',
  },
  sortContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  resultsText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#6B7280',
  },
  sortButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sortByText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    marginRight: 8,
  },
  sortButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    marginLeft: 4,
  },
  sortButtonActive: {
    backgroundColor: '#ECFDF5',
  },
  sortButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    color: '#6B7280',
  },
  sortButtonTextActive: {
    color: '#0B8457',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  venuesList: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  row: {
    justifyContent: 'space-between',
  },
  venueCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  venueImage: {
    width: '100%',
    height: 120,
  },
  featuredTag: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: '#FF6B00',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  featuredTagText: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 12,
    color: '#FFFFFF',
  },
  venueDetails: {
    padding: 12,
  },
  venueName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 18,
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  venueLocationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 2,
    flex: 1,
  },
  venueFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  featureTag: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 4,
  },
  featureTagText: {
    fontFamily: 'Inter-Medium',
    fontSize: 10,
    color: '#4B5563',
  },
  venueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  venueRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  venueRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 2,
  },
  venuePrice: {
    fontSize: 13,
    fontFamily: 'Inter-Bold',
    color: '#0B8457',
  },
  emptyState: {
    paddingVertical: 40,
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    color: '#374151',
    marginBottom: 8,
  },
  emptyStateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
  },
});