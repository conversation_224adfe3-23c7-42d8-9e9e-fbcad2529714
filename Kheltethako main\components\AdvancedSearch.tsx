import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Modal,
} from 'react-native';
import { Search, Filter, X, MapPin, Calendar, Clock, DollarSign } from 'lucide-react-native';

interface AdvancedSearchProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: SearchFilters) => void;
  initialFilters?: SearchFilters;
}

export interface SearchFilters {
  query: string;
  sports: string[];
  location: string;
  priceRange: [number, number];
  availability: string;
  amenities: string[];
  rating: number;
  sortBy: string;
}

const SPORTS_OPTIONS = [
  'Football', 'Cricket', 'Basketball', 'Tennis', 'Badminton', 
  'Swimming', 'Volleyball', 'Table Tennis', 'Squash', 'Hockey'
];

const AMENITIES_OPTIONS = [
  'Free Parking', 'Free WiFi', 'Security', 'Changing Rooms', 
  'CCTV', 'First Aid', 'Equipment Rental', 'Refreshments',
  'Air Conditioning', 'Sound System'
];

const SORT_OPTIONS = [
  { label: 'Relevance', value: 'relevance' },
  { label: 'Price: Low to High', value: 'price_low' },
  { label: 'Price: High to Low', value: 'price_high' },
  { label: 'Rating', value: 'rating' },
  { label: 'Distance', value: 'distance' },
];

export default function AdvancedSearch({ 
  visible, 
  onClose, 
  onApplyFilters, 
  initialFilters 
}: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>(
    initialFilters || {
      query: '',
      sports: [],
      location: '',
      priceRange: [0, 10000],
      availability: 'any',
      amenities: [],
      rating: 0,
      sortBy: 'relevance',
    }
  );

  const toggleSport = (sport: string) => {
    setFilters(prev => ({
      ...prev,
      sports: prev.sports.includes(sport)
        ? prev.sports.filter(s => s !== sport)
        : [...prev.sports, sport]
    }));
  };

  const toggleAmenity = (amenity: string) => {
    setFilters(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const resetFilters = () => {
    setFilters({
      query: '',
      sports: [],
      location: '',
      priceRange: [0, 10000],
      availability: 'any',
      amenities: [],
      rating: 0,
      sortBy: 'relevance',
    });
  };

  const applyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Advanced Search</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color="#111827" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Search Query */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Search</Text>
            <View style={styles.searchContainer}>
              <Search size={20} color="#6B7280" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search venues, sports, locations..."
                value={filters.query}
                onChangeText={(text) => setFilters(prev => ({ ...prev, query: text }))}
              />
            </View>
          </View>

          {/* Sports */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sports</Text>
            <View style={styles.optionsGrid}>
              {SPORTS_OPTIONS.map((sport) => (
                <TouchableOpacity
                  key={sport}
                  style={[
                    styles.optionChip,
                    filters.sports.includes(sport) && styles.optionChipSelected
                  ]}
                  onPress={() => toggleSport(sport)}
                >
                  <Text style={[
                    styles.optionChipText,
                    filters.sports.includes(sport) && styles.optionChipTextSelected
                  ]}>
                    {sport}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Location */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location</Text>
            <View style={styles.inputContainer}>
              <MapPin size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Enter area or city"
                value={filters.location}
                onChangeText={(text) => setFilters(prev => ({ ...prev, location: text }))}
              />
            </View>
          </View>

          {/* Price Range */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Price Range (per hour)</Text>
            <View style={styles.priceRangeContainer}>
              <View style={styles.priceInputContainer}>
                <DollarSign size={16} color="#6B7280" />
                <TextInput
                  style={styles.priceInput}
                  placeholder="Min"
                  value={filters.priceRange[0].toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    setFilters(prev => ({ 
                      ...prev, 
                      priceRange: [value, prev.priceRange[1]] 
                    }));
                  }}
                  keyboardType="numeric"
                />
              </View>
              <Text style={styles.priceRangeSeparator}>to</Text>
              <View style={styles.priceInputContainer}>
                <DollarSign size={16} color="#6B7280" />
                <TextInput
                  style={styles.priceInput}
                  placeholder="Max"
                  value={filters.priceRange[1].toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 10000;
                    setFilters(prev => ({ 
                      ...prev, 
                      priceRange: [prev.priceRange[0], value] 
                    }));
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>

          {/* Amenities */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            <View style={styles.optionsGrid}>
              {AMENITIES_OPTIONS.map((amenity) => (
                <TouchableOpacity
                  key={amenity}
                  style={[
                    styles.optionChip,
                    filters.amenities.includes(amenity) && styles.optionChipSelected
                  ]}
                  onPress={() => toggleAmenity(amenity)}
                >
                  <Text style={[
                    styles.optionChipText,
                    filters.amenities.includes(amenity) && styles.optionChipTextSelected
                  ]}>
                    {amenity}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Sort By */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sort By</Text>
            <View style={styles.sortOptions}>
              {SORT_OPTIONS.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.sortOption,
                    filters.sortBy === option.value && styles.sortOptionSelected
                  ]}
                  onPress={() => setFilters(prev => ({ ...prev, sortBy: option.value }))}
                >
                  <Text style={[
                    styles.sortOptionText,
                    filters.sortBy === option.value && styles.sortOptionTextSelected
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
            <Text style={styles.resetButtonText}>Reset</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
            <Text style={styles.applyButtonText}>Apply Filters</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  input: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    marginBottom: 8,
  },
  optionChipSelected: {
    backgroundColor: '#0B8457',
  },
  optionChipText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  optionChipTextSelected: {
    color: '#FFFFFF',
  },
  priceRangeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  priceInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  priceInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
  },
  priceRangeSeparator: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  sortOptions: {
    gap: 8,
  },
  sortOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
  },
  sortOptionSelected: {
    backgroundColor: '#0B8457',
  },
  sortOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  sortOptionTextSelected: {
    color: '#FFFFFF',
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  resetButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  applyButton: {
    flex: 2,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#0B8457',
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
