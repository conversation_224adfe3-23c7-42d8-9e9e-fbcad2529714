// Push Notification Service for KhelteThako
import { Platform } from 'react-native';
import { supabase } from '@/lib/supabase';

// Dynamic imports for native platforms only
const getNotifications = async () => {
  if (Platform.OS === 'web') return null;
  try {
    const { default: Notifications } = await import('expo-notifications');
    return Notifications;
  } catch (error) {
    console.warn('expo-notifications not available:', error);
    return null;
  }
};

const getDevice = async () => {
  if (Platform.OS === 'web') return null;
  try {
    const { default: Device } = await import('expo-device');
    return Device;
  } catch (error) {
    console.warn('expo-device not available:', error);
    return null;
  }
};

export interface PushNotificationToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
  device_id?: string;
}

/**
 * Register for push notifications and get token
 */
export const registerForPushNotifications = async (): Promise<string | null> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Push notifications not supported on web platform');
      return null;
    }

    const Notifications = await getNotifications();
    const Device = await getDevice();

    if (!Notifications || !Device) {
      console.log('Push notification modules not available');
      return null;
    }

    if (!Device.isDevice) {
      console.log('Must use physical device for Push Notifications');
      return null;
    }

    // Configure notification behavior
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });

    // Check existing permissions
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    // Request permissions if not granted
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    // Get the token
    const token = await Notifications.getExpoPushTokenAsync({
      projectId: process.env.EXPO_PUBLIC_PROJECT_ID || 'your-expo-project-id',
    });

    console.log('Push notification token:', token.data);

    // Configure notification channel for Android
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#0B8457',
      });

      // Create payment notifications channel
      await Notifications.setNotificationChannelAsync('payments', {
        name: 'Payment Notifications',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#0B8457',
        sound: 'default',
      });

      // Create booking notifications channel
      await Notifications.setNotificationChannelAsync('bookings', {
        name: 'Booking Notifications',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#0B8457',
        sound: 'default',
      });
    }

    return token.data;
  } catch (error) {
    console.error('Error registering for push notifications:', error);
    return null;
  }
};

/**
 * Save push notification token to Supabase
 */
export const savePushToken = async (userId: string, token: string): Promise<boolean> => {
  try {
    const platform = Platform.OS === 'web' ? 'web' : Platform.OS as 'ios' | 'android';
    const deviceId = await getDeviceId();

    const { error } = await supabase
      .from('push_notification_tokens')
      .upsert({
        user_id: userId,
        token: token,
        platform: platform,
        device_id: deviceId,
        is_active: true,
        last_used_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id,token'
      });

    if (error) {
      console.error('Error saving push token:', error);
      return false;
    }

    console.log('Push token saved successfully');
    return true;
  } catch (error) {
    console.error('Error in savePushToken:', error);
    return false;
  }
};

/**
 * Remove push notification token from Supabase
 */
export const removePushToken = async (userId: string, token: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('push_notification_tokens')
      .update({ is_active: false })
      .eq('user_id', userId)
      .eq('token', token);

    if (error) {
      console.error('Error removing push token:', error);
      return false;
    }

    console.log('Push token removed successfully');
    return true;
  } catch (error) {
    console.error('Error in removePushToken:', error);
    return false;
  }
};

/**
 * Send push notification via Supabase Edge Function
 */
export const sendPushNotification = async (
  userId: string,
  title: string,
  body: string,
  data?: any
): Promise<boolean> => {
  try {
    const { data: result, error } = await supabase.functions.invoke('push-notifications', {
      body: {
        user_id: userId,
        title: title,
        body: body,
        data: data,
        platform: 'all'
      }
    });

    if (error) {
      console.error('Error sending push notification:', error);
      return false;
    }

    console.log('Push notification sent:', result);
    return result.success;
  } catch (error) {
    console.error('Error in sendPushNotification:', error);
    return false;
  }
};

/**
 * Send broadcast notification to all users
 */
export const sendBroadcastNotification = async (
  title: string,
  body: string,
  data?: any
): Promise<boolean> => {
  try {
    const { data: result, error } = await supabase.functions.invoke('push-notifications', {
      body: {
        title: title,
        body: body,
        data: data,
        platform: 'all'
      }
    });

    if (error) {
      console.error('Error sending broadcast notification:', error);
      return false;
    }

    console.log('Broadcast notification sent:', result);
    return result.success;
  } catch (error) {
    console.error('Error in sendBroadcastNotification:', error);
    return false;
  }
};

/**
 * Handle notification received while app is in foreground
 */
export const handleNotificationReceived = (notification: any) => {
  console.log('Notification received:', notification);

  if (Platform.OS === 'web') {
    console.log('Web notification handling not implemented');
    return;
  }

  // You can customize the behavior here
  // For example, show a custom in-app notification
  const { title, body, data } = notification.request?.content || {};

  // Handle different notification types
  if (data?.type === 'payment') {
    // Handle payment notification
    console.log('Payment notification received:', data);
  } else if (data?.type === 'booking') {
    // Handle booking notification
    console.log('Booking notification received:', data);
  }
};

/**
 * Handle notification tap (when user taps on notification)
 */
export const handleNotificationResponse = (response: any) => {
  console.log('Notification tapped:', response);

  if (Platform.OS === 'web') {
    console.log('Web notification response handling not implemented');
    return;
  }

  const { data } = response.notification?.request?.content || {};

  // Navigate based on notification type
  if (data?.type === 'payment' && data?.booking_id) {
    // Navigate to booking details
    // router.push(`/booking/${data.booking_id}`);
  } else if (data?.type === 'booking' && data?.booking_id) {
    // Navigate to booking details
    // router.push(`/booking/${data.booking_id}`);
  }
};

/**
 * Get device ID for tracking
 */
const getDeviceId = async (): Promise<string> => {
  try {
    if (Platform.OS === 'web') {
      return 'web-device';
    }

    const Device = await getDevice();
    if (!Device) {
      return 'unknown-device';
    }

    // In a real app, you might want to use a more persistent device ID
    // For now, we'll use a combination of device info
    const deviceInfo = await Device.getDeviceTypeAsync();
    const osVersion = Device.osVersion;
    const modelName = Device.modelName;

    return `${deviceInfo}-${osVersion}-${modelName}`.replace(/\s+/g, '-');
  } catch (error) {
    console.error('Error getting device ID:', error);
    return 'unknown-device';
  }
};

/**
 * Initialize push notifications for a user
 */
export const initializePushNotifications = async (userId: string): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Push notifications not supported on web platform');
      return false;
    }

    // Register for push notifications
    const token = await registerForPushNotifications();

    if (!token) {
      console.log('Failed to get push notification token');
      return false;
    }

    // Save token to Supabase
    const saved = await savePushToken(userId, token);

    if (!saved) {
      console.log('Failed to save push notification token');
      return false;
    }

    const Notifications = await getNotifications();
    if (Notifications) {
      // Set up notification listeners
      const notificationListener = Notifications.addNotificationReceivedListener(handleNotificationReceived);
      const responseListener = Notifications.addNotificationResponseReceivedListener(handleNotificationResponse);

      // Store listeners for cleanup
      // You might want to store these in a context or global state
      console.log('Push notifications initialized successfully');
    }

    return true;
  } catch (error) {
    console.error('Error initializing push notifications:', error);
    return false;
  }
};

/**
 * Cleanup push notifications when user logs out
 */
export const cleanupPushNotifications = async (userId: string): Promise<void> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Push notifications cleanup (web)');
      return;
    }

    const Notifications = await getNotifications();
    if (Notifications) {
      // Get current token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID || 'your-expo-project-id',
      });

      if (token) {
        // Remove token from Supabase
        await removePushToken(userId, token.data);
      }

      // Remove notification listeners
      Notifications.removeAllNotificationListeners();
    }

    console.log('Push notifications cleaned up');
  } catch (error) {
    console.error('Error cleaning up push notifications:', error);
  }
};

/**
 * Check notification permissions status
 */
export const getNotificationPermissionStatus = async (): Promise<string> => {
  try {
    if (Platform.OS === 'web') {
      return 'granted'; // Web doesn't need permissions for basic notifications
    }

    const Notifications = await getNotifications();
    if (!Notifications) {
      return 'undetermined';
    }

    const { status } = await Notifications.getPermissionsAsync();
    return status;
  } catch (error) {
    console.error('Error getting notification permission status:', error);
    return 'undetermined';
  }
};

/**
 * Request notification permissions
 */
export const requestNotificationPermissions = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      return true; // Web doesn't need permissions for basic notifications
    }

    const Notifications = await getNotifications();
    if (!Notifications) {
      return false;
    }

    const { status } = await Notifications.requestPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
};

export default {
  registerForPushNotifications,
  savePushToken,
  removePushToken,
  sendPushNotification,
  sendBroadcastNotification,
  initializePushNotifications,
  cleanupPushNotifications,
  getNotificationPermissionStatus,
  requestNotificationPermissions,
  handleNotificationReceived,
  handleNotificationResponse,
};
