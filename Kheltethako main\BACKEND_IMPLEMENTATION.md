# KhelteThako Backend Implementation

## 🎯 Overview

This document outlines the comprehensive backend implementation for KhelteThako, a sports venue and coach booking platform. The implementation includes push notifications, payment webhooks, and analytics using Supabase as the backend infrastructure.

## 🏗️ Architecture

### Backend Services
- **Supabase**: PostgreSQL database, authentication, real-time subscriptions
- **Supabase Edge Functions**: Serverless functions for webhooks and notifications
- **UddoktaPay**: Payment gateway integration for Bangladesh
- **Expo Push Notifications**: Cross-platform push notification service

### Key Components
1. **Push Notification Service** (`services/pushNotificationService.ts`)
2. **Payment Service** (`services/paymentService.ts`)
3. **Analytics Service** (`services/analyticsService.ts`)
4. **Supabase Edge Functions** (3 functions deployed)

## 📱 Push Notifications

### Features
- ✅ Cross-platform support (iOS, Android, Web)
- ✅ Dynamic imports for web compatibility
- ✅ Token management and storage
- ✅ Notification channels for different types
- ✅ Real-time delivery via Expo Push API

### Database Schema
```sql
-- Push notification tokens table
CREATE TABLE push_notification_tokens (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    token TEXT NOT NULL,
    platform VARCHAR(20) CHECK (platform IN ('ios', 'android', 'web')),
    device_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMPTZ DEFAULT now(),
    created_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(user_id, token)
);
```

### Edge Function: `push-notifications`
- **Purpose**: Send push notifications to users
- **Features**: 
  - User-specific and broadcast notifications
  - Platform filtering
  - Expo Push API integration
  - Database logging

### Usage Example
```typescript
import { initializePushNotifications, sendPushNotification } from '@/services/pushNotificationService';

// Initialize for a user
await initializePushNotifications(userId);

// Send notification
await sendPushNotification(
  userId,
  "Booking Confirmed",
  "Your venue booking has been confirmed!",
  { type: 'booking', booking_id: '123' }
);
```

## 💳 Payment Integration

### UddoktaPay Integration
- ✅ Payment initialization
- ✅ Webhook handling
- ✅ Payment verification
- ✅ Refund processing
- ✅ Payment history tracking

### Database Schema
```sql
-- Payment records table
CREATE TABLE payment_records (
    id SERIAL PRIMARY KEY,
    invoice_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id),
    booking_id INTEGER REFERENCES bookings(id),
    session_id INTEGER REFERENCES coach_sessions(id),
    booking_type VARCHAR(20) CHECK (booking_type IN ('venue', 'coach')),
    amount NUMERIC(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    payment_url TEXT,
    transaction_id VARCHAR(255),
    uddoktapay_response JSONB,
    created_at TIMESTAMPTZ DEFAULT now()
);
```

### Edge Function: `payment-webhook`
- **Purpose**: Handle UddoktaPay webhook notifications
- **Features**:
  - Payment status updates
  - Booking/session confirmation
  - Automatic push notifications
  - Error handling and logging

### Usage Example
```typescript
import { initializePayment } from '@/services/paymentService';

const paymentResult = await initializePayment({
  amount: 1500,
  user_id: userId,
  booking_id: bookingId,
  booking_type: 'venue',
  customer_name: 'John Doe',
  customer_email: '<EMAIL>'
});

if (paymentResult.success) {
  // Redirect to payment URL
  window.location.href = paymentResult.payment_url;
}
```

## 📊 Analytics

### Features
- ✅ Event tracking (page views, bookings, searches)
- ✅ User behavior analytics
- ✅ Real-time data collection
- ✅ CORS support for web
- ✅ Privacy-compliant tracking

### Database Schema
```sql
-- Analytics events table
CREATE TABLE analytics_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(255),
    page_url TEXT,
    venue_id INTEGER REFERENCES venues(id),
    coach_id INTEGER REFERENCES coaches(id),
    search_query TEXT,
    client_ip VARCHAR(45),
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now()
);
```

### Edge Function: `analytics`
- **Purpose**: Collect and store analytics events
- **Features**:
  - Event validation
  - IP and user agent tracking
  - View count updates
  - CORS handling

### Usage Example
```typescript
import { trackPageView, trackBookingCompleted } from '@/services/analyticsService';

// Track page view
await trackPageView('/venue/123', userId, '123');

// Track booking completion
await trackBookingCompleted(userId, '123', undefined, 'BOOK123', 1500);
```

## 🚀 Deployment

### Supabase Edge Functions
All functions are deployed and active:
1. `push-notifications` - Handle push notification sending
2. `payment-webhook` - Process UddoktaPay webhooks
3. `analytics` - Collect analytics events

### Environment Variables
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_UDDOKTAPAY_API_KEY=your_uddoktapay_key
EXPO_PUBLIC_UDDOKTAPAY_API_URL=https://sandbox.uddoktapay.com/api/checkout-v2
EXPO_PUBLIC_WEBHOOK_URL=your_webhook_url
EXPO_PUBLIC_PROJECT_ID=your_expo_project_id
```

## 🔧 Technical Implementation

### Web Compatibility
- Dynamic imports for Expo modules
- Platform-specific code paths
- Graceful fallbacks for web platform
- Metro bundler configuration

### Error Handling
- Comprehensive try-catch blocks
- Logging for debugging
- Graceful degradation
- User-friendly error messages

### Security
- JWT verification on Edge Functions
- Input validation
- SQL injection prevention
- CORS configuration

## 📈 Monitoring & Analytics

### Key Metrics Tracked
- Page views and user sessions
- Booking conversion rates
- Payment success/failure rates
- Search queries and results
- User engagement patterns

### Database Views
```sql
-- Analytics summary view
CREATE VIEW analytics_summary AS
SELECT 
    event_type,
    COUNT(*) as event_count,
    COUNT(DISTINCT user_id) as unique_users,
    DATE_TRUNC('day', created_at) as event_date
FROM analytics_events
GROUP BY event_type, DATE_TRUNC('day', created_at);
```

## 🧪 Testing

### Development Server
```bash
npm start
# or
npx expo start --web
```

### Testing Push Notifications
1. Register for notifications in the app
2. Use Supabase dashboard to call the push-notifications function
3. Verify delivery on device/browser

### Testing Payments
1. Use UddoktaPay sandbox environment
2. Test webhook delivery
3. Verify database updates

## 🔮 Future Enhancements

1. **Advanced Analytics**: User cohort analysis, retention metrics
2. **A/B Testing**: Feature flag system
3. **Real-time Chat**: Coach-player communication
4. **Advanced Notifications**: Scheduled notifications, rich media
5. **Performance Monitoring**: APM integration

## 📚 Documentation

- [Supabase Documentation](https://supabase.com/docs)
- [Expo Push Notifications](https://docs.expo.dev/push-notifications/overview/)
- [UddoktaPay API](https://uddoktapay.com/docs)
- [React Native Documentation](https://reactnative.dev/docs)

---

**Status**: ✅ Fully Implemented and Tested
**Last Updated**: January 2025
**Version**: 1.0.0
