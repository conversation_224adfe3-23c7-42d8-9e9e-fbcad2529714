import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  Shield,
  Eye,
  Bell,
  MapPin,
  Users,
  Lock,
  Database,
  Trash2,
  Download,
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

interface PrivacySettings {
  profile_visibility: 'public' | 'friends' | 'private';
  show_booking_history: boolean;
  show_location: boolean;
  allow_friend_requests: boolean;
  marketing_emails: boolean;
  push_notifications: boolean;
  location_tracking: boolean;
  data_analytics: boolean;
  third_party_sharing: boolean;
}

export default function PrivacySettingsScreen() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState<PrivacySettings>({
    profile_visibility: 'public',
    show_booking_history: false,
    show_location: true,
    allow_friend_requests: true,
    marketing_emails: false,
    push_notifications: true,
    location_tracking: false,
    data_analytics: true,
    third_party_sharing: false,
  });

  useEffect(() => {
    fetchPrivacySettings();
  }, []);

  const fetchPrivacySettings = async () => {
    try {
      setIsLoading(true);
      if (!user?.id) return;

      const { data, error } = await supabase
        .from('user_privacy_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setSettings({
          profile_visibility: data.profile_visibility || 'public',
          show_booking_history: data.show_booking_history || false,
          show_location: data.show_location || true,
          allow_friend_requests: data.allow_friend_requests || true,
          marketing_emails: data.marketing_emails || false,
          push_notifications: data.push_notifications || true,
          location_tracking: data.location_tracking || false,
          data_analytics: data.data_analytics || true,
          third_party_sharing: data.third_party_sharing || false,
        });
      }
    } catch (error) {
      console.error('Error fetching privacy settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = async (key: keyof PrivacySettings, value: any) => {
    try {
      setIsSaving(true);
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);

      if (!user?.id) return;

      const { error } = await supabase
        .from('user_privacy_settings')
        .upsert({
          user_id: user.id,
          ...newSettings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error updating privacy setting:', error);
      Alert.alert('Error', 'Failed to update privacy setting');
      // Revert the change
      setSettings(prev => ({ ...prev, [key]: !value }));
    } finally {
      setIsSaving(false);
    }
  };

  const handleDataExport = () => {
    Alert.alert(
      'Export Data',
      'We will prepare your data export and send it to your email address within 24 hours.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Request Export', onPress: () => console.log('Data export requested') },
      ]
    );
  };

  const handleAccountDeletion = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Final Confirmation',
              'Are you absolutely sure you want to delete your account?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Yes, Delete', style: 'destructive', onPress: () => console.log('Account deletion requested') },
              ]
            );
          },
        },
      ]
    );
  };

  const SettingItem = ({
    icon,
    title,
    description,
    value,
    onValueChange,
    type = 'switch',
    options,
  }: {
    icon: React.ReactNode;
    title: string;
    description: string;
    value: any;
    onValueChange: (value: any) => void;
    type?: 'switch' | 'select';
    options?: { label: string; value: any }[];
  }) => (
    <View style={styles.settingItem}>
      <View style={styles.settingIcon}>
        {icon}
      </View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        <Text style={styles.settingDescription}>{description}</Text>
        {type === 'select' && options && (
          <View style={styles.optionsContainer}>
            {options.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.optionButton,
                  value === option.value && styles.optionButtonSelected,
                ]}
                onPress={() => onValueChange(option.value)}
              >
                <Text
                  style={[
                    styles.optionText,
                    value === option.value && styles.optionTextSelected,
                  ]}
                >
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#E5E7EB', true: '#D1FAE5' }}
          thumbColor={value ? '#0B8457' : '#9CA3AF'}
          disabled={isSaving}
        />
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={20} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Privacy Settings</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0B8457" />
          <Text style={styles.loadingText}>Loading privacy settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Privacy Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Privacy</Text>
          
          <SettingItem
            icon={<Eye size={20} color="#0B8457" />}
            title="Profile Visibility"
            description="Control who can see your profile information"
            value={settings.profile_visibility}
            onValueChange={(value) => updateSetting('profile_visibility', value)}
            type="select"
            options={[
              { label: 'Public', value: 'public' },
              { label: 'Friends Only', value: 'friends' },
              { label: 'Private', value: 'private' },
            ]}
          />

          <SettingItem
            icon={<Users size={20} color="#0B8457" />}
            title="Allow Friend Requests"
            description="Let other users send you friend requests"
            value={settings.allow_friend_requests}
            onValueChange={(value) => updateSetting('allow_friend_requests', value)}
          />

          <SettingItem
            icon={<MapPin size={20} color="#0B8457" />}
            title="Show Location"
            description="Display your location on your profile"
            value={settings.show_location}
            onValueChange={(value) => updateSetting('show_location', value)}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Privacy</Text>
          
          <SettingItem
            icon={<Shield size={20} color="#0B8457" />}
            title="Show Booking History"
            description="Allow others to see your past bookings"
            value={settings.show_booking_history}
            onValueChange={(value) => updateSetting('show_booking_history', value)}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Communication</Text>
          
          <SettingItem
            icon={<Bell size={20} color="#0B8457" />}
            title="Push Notifications"
            description="Receive notifications about bookings and updates"
            value={settings.push_notifications}
            onValueChange={(value) => updateSetting('push_notifications', value)}
          />

          <SettingItem
            icon={<Bell size={20} color="#0B8457" />}
            title="Marketing Emails"
            description="Receive promotional emails and offers"
            value={settings.marketing_emails}
            onValueChange={(value) => updateSetting('marketing_emails', value)}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data & Analytics</Text>
          
          <SettingItem
            icon={<MapPin size={20} color="#0B8457" />}
            title="Location Tracking"
            description="Allow app to track your location for better recommendations"
            value={settings.location_tracking}
            onValueChange={(value) => updateSetting('location_tracking', value)}
          />

          <SettingItem
            icon={<Database size={20} color="#0B8457" />}
            title="Usage Analytics"
            description="Help improve the app by sharing anonymous usage data"
            value={settings.data_analytics}
            onValueChange={(value) => updateSetting('data_analytics', value)}
          />

          <SettingItem
            icon={<Lock size={20} color="#0B8457" />}
            title="Third-party Sharing"
            description="Allow sharing data with trusted partners"
            value={settings.third_party_sharing}
            onValueChange={(value) => updateSetting('third_party_sharing', value)}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleDataExport}>
            <Download size={20} color="#3B82F6" />
            <View style={styles.actionButtonContent}>
              <Text style={styles.actionButtonTitle}>Export My Data</Text>
              <Text style={styles.actionButtonDescription}>
                Download a copy of all your data
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionButton, styles.dangerButton]} onPress={handleAccountDeletion}>
            <Trash2 size={20} color="#EF4444" />
            <View style={styles.actionButtonContent}>
              <Text style={[styles.actionButtonTitle, styles.dangerText]}>Delete Account</Text>
              <Text style={styles.actionButtonDescription}>
                Permanently delete your account and all data
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 12,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ECFDF5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
    marginRight: 12,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 18,
  },
  optionsContainer: {
    flexDirection: 'row',
    marginTop: 12,
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  optionButtonSelected: {
    backgroundColor: '#ECFDF5',
    borderColor: '#0B8457',
  },
  optionText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  optionTextSelected: {
    color: '#0B8457',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dangerButton: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
  },
  actionButtonContent: {
    flex: 1,
    marginLeft: 12,
  },
  actionButtonTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  dangerText: {
    color: '#EF4444',
  },
  actionButtonDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
});
