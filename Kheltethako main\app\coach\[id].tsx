import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Heart, MapPin, Star, Calendar, Clock, Share2, Award, CircleCheck as CheckCircle2 } from 'lucide-react-native';
import AvailabilityCalendar from '@/components/AvailabilityCalendar';

const { width } = Dimensions.get('window');

// This would normally come from the API
const getCoachDetails = async (id) => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    id: parseInt(id),
    name: '<PERSON><PERSON><PERSON><PERSON>',
    specialty: 'Cricket Bowling Coach',
    location: 'Mirpur Cricket Stadium, Dhaka',
    rating: 4.9,
    reviews_count: 87,
    price_per_session: 5000,
    image_url: 'https://images.pexels.com/photos/6551155/pexels-photo-6551155.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    images: [
      'https://images.pexels.com/photos/6551155/pexels-photo-6551155.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      'https://images.pexels.com/photos/6551090/pexels-photo-6551090.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      'https://images.pexels.com/photos/9594685/pexels-photo-9594685.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    ],
    bio: 'Former Bangladesh cricket team captain with over 15 years of international experience. Specializing in fast-medium bowling techniques, match strategy, and mental strength training for cricketers of all levels.',
    experience: '15+ years of international cricket experience',
    certifications: [
      'ICC Level 3 Coaching Certification',
      'Cricket Australia Advanced Coaching License',
      'Sports Nutrition Specialist',
    ],
    specialties: [
      'Fast-Medium Bowling Technique',
      'Cricket Strategy and Tactics',
      'Mental Strength Training',
      'Youth Development',
    ],
    available_dates: [
      '2025-05-10',
      '2025-05-11',
      '2025-05-12',
      '2025-05-15',
      '2025-05-16',
      '2025-05-17',
    ],
    time_slots: [
      '09:00 - 10:00',
      '10:00 - 11:00',
      '11:00 - 12:00',
      '14:00 - 15:00',
      '15:00 - 16:00',
      '16:00 - 17:00',
    ],
  };
};

export default function CoachDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [coach, setCoach] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [activeTab, setActiveTab] = useState('about');
  const [showAvailability, setShowAvailability] = useState(false);

  useEffect(() => {
    fetchCoachDetails();
  }, [id]);

  const fetchCoachDetails = async () => {
    try {
      setIsLoading(true);
      const data = await getCoachDetails(id);
      setCoach(data);
      
      // Set first available date as default selected
      if (data.available_dates && data.available_dates.length > 0) {
        setSelectedDate(data.available_dates[0]);
      }
    } catch (error) {
      console.error('Error fetching coach details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const handleTimeSlotSelect = (timeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleBookSession = () => {
    if (!selectedDate || !selectedTimeSlot) return;

    router.push({
      pathname: '/coach-booking-confirmation',
      params: {
        coachId: id,
        coachName: coach?.name,
        coachImage: coach?.image_url,
        specialty: coach?.specialty,
        rating: coach?.average_rating?.toString(),
        reviewCount: coach?.total_reviews?.toString(),
        location: coach?.location,
        date: selectedDate,
        timeSlot: JSON.stringify(selectedTimeSlot),
        price: selectedTimeSlot?.price?.toString(),
      },
    });
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0B8457" />
      </SafeAreaView>
    );
  }

  if (!coach) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>Coach not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image source={{ uri: coach.image_url }} style={styles.coachImage} />
          <View style={styles.imageOverlay}>
            <TouchableOpacity
              style={styles.backButtonCircle}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={toggleFavorite}
            >
              <Heart
                size={20}
                color="#FFFFFF"
                fill={isFavorite ? '#EF4444' : 'transparent'}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.coachName}>{coach.name}</Text>
          <Text style={styles.coachSpecialty}>{coach.specialty}</Text>
          
          <View style={styles.locationContainer}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.locationText}>{coach.location}</Text>
          </View>

          <View style={styles.ratingContainer}>
            <View style={styles.rating}>
              <Star size={16} color="#F59E0B" fill="#F59E0B" />
              <Text style={styles.ratingText}>
                {coach.rating} ({coach.reviews_count} reviews)
              </Text>
            </View>
            <TouchableOpacity style={styles.shareButton}>
              <Share2 size={16} color="#6B7280" />
              <Text style={styles.shareText}>Share</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.tabsContainer}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'about' && styles.activeTabButton,
              ]}
              onPress={() => setActiveTab('about')}
            >
              <Text
                style={[
                  styles.tabButtonText,
                  activeTab === 'about' && styles.activeTabButtonText,
                ]}
              >
                About
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'experience' && styles.activeTabButton,
              ]}
              onPress={() => setActiveTab('experience')}
            >
              <Text
                style={[
                  styles.tabButtonText,
                  activeTab === 'experience' && styles.activeTabButtonText,
                ]}
              >
                Experience
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'reviews' && styles.activeTabButton,
              ]}
              onPress={() => setActiveTab('reviews')}
            >
              <Text
                style={[
                  styles.tabButtonText,
                  activeTab === 'reviews' && styles.activeTabButtonText,
                ]}
              >
                Reviews
              </Text>
            </TouchableOpacity>
          </View>

          {activeTab === 'about' && (
            <>
              <Text style={styles.sectionTitle}>Bio</Text>
              <Text style={styles.bioText}>{coach.bio}</Text>

              <Text style={styles.sectionTitle}>Specialties</Text>
              <View style={styles.specialtiesContainer}>
                {coach.specialties.map((specialty, index) => (
                  <View key={index} style={styles.specialtyItem}>
                    <CheckCircle2 size={16} color="#0B8457" />
                    <Text style={styles.specialtyText}>{specialty}</Text>
                  </View>
                ))}
              </View>
            </>
          )}

          {activeTab === 'experience' && (
            <>
              <Text style={styles.sectionTitle}>Experience</Text>
              <View style={styles.experienceItem}>
                <Clock size={18} color="#0B8457" />
                <Text style={styles.experienceText}>{coach.experience}</Text>
              </View>

              <Text style={styles.sectionTitle}>Certifications</Text>
              <View style={styles.certificationsContainer}>
                {coach.certifications.map((cert, index) => (
                  <View key={index} style={styles.certificationItem}>
                    <Award size={16} color="#0B8457" />
                    <Text style={styles.certificationText}>{cert}</Text>
                  </View>
                ))}
              </View>
            </>
          )}

          {activeTab === 'reviews' && (
            <>
              <View style={styles.reviewsSummary}>
                <Text style={styles.reviewsRating}>{coach.rating}</Text>
                <View style={styles.starsContainer}>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      size={16}
                      color="#F59E0B"
                      fill={star <= Math.round(coach.rating) ? '#F59E0B' : 'transparent'}
                    />
                  ))}
                </View>
                <Text style={styles.reviewsCount}>
                  Based on {coach.reviews_count} reviews
                </Text>
              </View>
              
              <TouchableOpacity
                style={styles.viewAllButton}
                onPress={() => router.push(`/coach/${id}/reviews`)}
              >
                <Text style={styles.viewAllButtonText}>View All Reviews</Text>
              </TouchableOpacity>
            </>
          )}

          <View style={styles.divider} />

          <Text style={styles.sectionTitle}>Available Dates</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.dateContainer}
          >
            {coach.available_dates.map((date, index) => {
              const dateObj = new Date(date);
              const day = dateObj.getDate();
              const month = dateObj.toLocaleString('default', { month: 'short' });
              const dayName = dateObj.toLocaleString('default', { weekday: 'short' });
              
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dateItem,
                    selectedDate === date && styles.dateItemSelected,
                  ]}
                  onPress={() => setSelectedDate(date)}
                >
                  <Text
                    style={[
                      styles.dateDay,
                      selectedDate === date && styles.dateTextSelected,
                    ]}
                  >
                    {dayName}
                  </Text>
                  <Text
                    style={[
                      styles.dateNumber,
                      selectedDate === date && styles.dateTextSelected,
                    ]}
                  >
                    {day}
                  </Text>
                  <Text
                    style={[
                      styles.dateMonth,
                      selectedDate === date && styles.dateTextSelected,
                    ]}
                  >
                    {month}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          <View style={styles.bookingSection}>
            <Text style={styles.sectionTitle}>Book Your Session</Text>
            <TouchableOpacity
              style={styles.availabilityButton}
              onPress={() => setShowAvailability(!showAvailability)}
            >
              <Calendar size={20} color="#0B8457" />
              <Text style={styles.availabilityButtonText}>
                {selectedDate ? `Selected: ${new Date(selectedDate).toLocaleDateString()}` : 'Select Date & Time'}
              </Text>
              {selectedTimeSlot && (
                <Text style={styles.selectedTimeText}>
                  {selectedTimeSlot.start_time} - {selectedTimeSlot.end_time}
                </Text>
              )}
            </TouchableOpacity>

            {showAvailability && (
              <View style={styles.availabilityContainer}>
                <AvailabilityCalendar
                  coachId={parseInt(id as string)}
                  selectedDate={selectedDate}
                  onTimeSlotSelect={handleTimeSlotSelect}
                  selectedTimeSlot={selectedTimeSlot}
                />
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>Session Price</Text>
          <Text style={styles.price}>
            ৳{coach.price_per_session}
            <Text style={styles.perSession}>/session</Text>
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.bookButton,
            (!selectedDate || !selectedTimeSlot) && styles.bookButtonDisabled,
          ]}
          onPress={handleBookSession}
          disabled={!selectedDate || !selectedTimeSlot}
        >
          <Text style={styles.bookButtonText}>Book Session</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
    marginBottom: 16,
  },
  backButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#0B8457',
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  coachImage: {
    width: '100%',
    height: 300,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  backButtonCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 20,
  },
  coachName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  coachSpecialty: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 6,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shareText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 20,
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 16,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTabButton: {
    borderBottomColor: '#0B8457',
  },
  tabButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  activeTabButtonText: {
    color: '#0B8457',
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
    marginTop: 8,
  },
  bioText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 22,
    marginBottom: 20,
  },
  specialtiesContainer: {
    marginBottom: 20,
  },
  specialtyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  specialtyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    marginLeft: 8,
  },
  experienceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  experienceText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
    marginLeft: 8,
  },
  certificationsContainer: {
    marginBottom: 20,
  },
  certificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  certificationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    marginLeft: 8,
  },
  reviewsSummary: {
    alignItems: 'center',
    marginBottom: 20,
  },
  reviewsRating: {
    fontSize: 48,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  reviewsCount: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  viewAllButton: {
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#0B8457',
    borderRadius: 8,
    marginBottom: 20,
  },
  viewAllButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 20,
  },
  dateContainer: {
    paddingVertical: 10,
    marginBottom: 20,
  },
  dateItem: {
    width: 60,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateItemSelected: {
    backgroundColor: '#0B8457',
  },
  dateDay: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 2,
  },
  dateMonth: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  dateTextSelected: {
    color: '#FFFFFF',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  timeItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
    marginRight: 10,
    marginBottom: 10,
  },
  timeItemSelected: {
    backgroundColor: '#0B8457',
  },
  timeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  timeTextSelected: {
    color: '#FFFFFF',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  priceContainer: {
    flex: 1,
  },
  priceText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  price: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  perSession: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookButton: {
    backgroundColor: '#0B8457',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  bookButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  bookButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  bookingSection: {
    marginTop: 20,
  },
  availabilityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 16,
  },
  availabilityButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 8,
    flex: 1,
  },
  selectedTimeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
  },
  availabilityContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
    marginBottom: 16,
  },
});