import { router } from 'expo-router';
import { Alert } from 'react-native';

export const requireAuth = (user: any, action: string = 'perform this action') => {
  if (!user) {
    Alert.alert(
      'Authentication Required',
      `Please sign in to ${action}`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign In',
          onPress: () => router.push('/(auth)/login'),
        },
      ]
    );
    return false;
  }
  return true;
};

export const requireAuthWithCallback = (
  user: any, 
  callback: () => void, 
  action: string = 'perform this action'
) => {
  if (!user) {
    Alert.alert(
      'Authentication Required',
      `Please sign in to ${action}`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign In',
          onPress: () => router.push('/(auth)/login'),
        },
      ]
    );
    return;
  }
  callback();
};
