// UddoktaPay Webhook Handler - Supabase Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, uddoktapay-signature',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface WebhookPayload {
  invoice_id: string;
  status: 'COMPLETED' | 'PENDING' | 'ERROR';
  amount: string;
  fee: string;
  charged_amount: string;
  payment_method: string;
  sender_number: string;
  transaction_id: string;
  date: string;
  metadata: any;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get webhook signature for validation
    const signature = req.headers.get('uddoktapay-signature')
    const rawBody = await req.text()
    
    // Log webhook receipt
    console.log('Webhook received:', {
      signature,
      bodyLength: rawBody.length,
      timestamp: new Date().toISOString()
    })

    // Parse webhook payload
    const payload: WebhookPayload = JSON.parse(rawBody)
    
    // Store webhook event for audit trail
    const { error: webhookError } = await supabase
      .from('webhook_events')
      .insert({
        event_type: 'payment_update',
        source: 'uddoktapay',
        invoice_id: payload.invoice_id,
        payload: payload,
        headers: Object.fromEntries(req.headers.entries()),
        signature: signature,
        status: 'processing'
      })

    if (webhookError) {
      console.error('Error storing webhook event:', webhookError)
    }

    // Validate webhook signature (implement based on UddoktaPay docs)
    const isValidSignature = await validateWebhookSignature(rawBody, signature)
    if (!isValidSignature) {
      console.error('Invalid webhook signature')
      return new Response(
        JSON.stringify({ error: 'Invalid signature' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Process payment based on status
    const result = await processPaymentWebhook(supabase, payload)
    
    // Update webhook event status
    await supabase
      .from('webhook_events')
      .update({ 
        status: result.success ? 'processed' : 'failed',
        processed_at: new Date().toISOString(),
        error_message: result.error || null
      })
      .eq('invoice_id', payload.invoice_id)
      .eq('status', 'processing')

    if (result.success) {
      return new Response(
        JSON.stringify({ message: 'Webhook processed successfully' }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({ error: result.error }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function validateWebhookSignature(payload: string, signature: string | null): Promise<boolean> {
  // Implement signature validation based on UddoktaPay documentation
  // For now, return true (implement proper validation in production)
  console.log('Validating webhook signature...')
  return true
}

async function processPaymentWebhook(supabase: any, payload: WebhookPayload) {
  try {
    // Find existing payment transaction
    const { data: existingTransaction, error: findError } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('invoice_id', payload.invoice_id)
      .single()

    if (findError && findError.code !== 'PGRST116') {
      throw new Error(`Error finding transaction: ${findError.message}`)
    }

    // Update or create payment transaction
    const transactionData = {
      invoice_id: payload.invoice_id,
      transaction_id: payload.transaction_id,
      payment_method: payload.payment_method,
      sender_number: payload.sender_number,
      amount: parseFloat(payload.amount),
      fee: parseFloat(payload.fee),
      charged_amount: parseFloat(payload.charged_amount),
      status: payload.status.toLowerCase(),
      payment_date: payload.date,
      metadata: payload.metadata,
      updated_at: new Date().toISOString()
    }

    if (existingTransaction) {
      // Update existing transaction
      const { error: updateError } = await supabase
        .from('payment_transactions')
        .update(transactionData)
        .eq('id', existingTransaction.id)

      if (updateError) {
        throw new Error(`Error updating transaction: ${updateError.message}`)
      }
    } else {
      // Create new transaction (shouldn't happen in normal flow)
      const { error: insertError } = await supabase
        .from('payment_transactions')
        .insert(transactionData)

      if (insertError) {
        throw new Error(`Error creating transaction: ${insertError.message}`)
      }
    }

    // Update booking status based on payment status
    if (payload.metadata?.booking_id) {
      await updateBookingStatus(supabase, payload)
    }

    // Send push notification to user
    if (payload.metadata?.user_id) {
      await sendPaymentNotification(supabase, payload)
    }

    // Update analytics
    await updatePaymentAnalytics(supabase, payload)

    return { success: true }

  } catch (error) {
    console.error('Error processing payment webhook:', error)
    return { success: false, error: error.message }
  }
}

async function updateBookingStatus(supabase: any, payload: WebhookPayload) {
  const bookingStatus = payload.status === 'COMPLETED' ? 'confirmed' : 
                       payload.status === 'PENDING' ? 'pending_payment' : 'payment_failed'

  const { error } = await supabase
    .from('bookings')
    .update({ 
      status: bookingStatus,
      updated_at: new Date().toISOString()
    })
    .eq('id', payload.metadata.booking_id)

  if (error) {
    console.error('Error updating booking status:', error)
  }
}

async function sendPaymentNotification(supabase: any, payload: WebhookPayload) {
  const notificationData = {
    user_id: payload.metadata.user_id,
    title: payload.status === 'COMPLETED' ? 'Payment Successful' : 
           payload.status === 'PENDING' ? 'Payment Pending' : 'Payment Failed',
    message: payload.status === 'COMPLETED' 
      ? `Your payment of ৳${payload.charged_amount} has been confirmed. Transaction ID: ${payload.transaction_id}`
      : payload.status === 'PENDING'
      ? `Your payment of ৳${payload.amount} is being processed. We'll notify you once confirmed.`
      : `Your payment of ৳${payload.amount} could not be processed. Please try again.`,
    type: 'payment',
    action_url: `/booking/${payload.metadata.booking_id}`,
    is_read: false
  }

  // Create notification record
  const { data: notification, error: notifError } = await supabase
    .from('notifications')
    .insert(notificationData)
    .select()
    .single()

  if (notifError) {
    console.error('Error creating notification:', notifError)
    return
  }

  // Queue push notification
  await supabase
    .from('notification_queue')
    .insert({
      user_id: payload.metadata.user_id,
      notification_id: notification.id,
      title: notificationData.title,
      body: notificationData.message,
      data: {
        type: 'payment',
        booking_id: payload.metadata.booking_id,
        transaction_id: payload.transaction_id
      },
      platform: 'all'
    })
}

async function updatePaymentAnalytics(supabase: any, payload: WebhookPayload) {
  const paymentDate = new Date(payload.date)
  const date = paymentDate.toISOString().split('T')[0]
  const hour = paymentDate.getHours()
  
  // Upsert analytics record
  const { error } = await supabase.rpc('upsert_payment_analytics', {
    p_date: date,
    p_hour: hour,
    p_status: payload.status.toLowerCase(),
    p_amount: parseFloat(payload.charged_amount),
    p_fee: parseFloat(payload.fee),
    p_payment_method: payload.payment_method,
    p_booking_type: payload.metadata?.booking_type || 'venue'
  })

  if (error) {
    console.error('Error updating analytics:', error)
  }
}
