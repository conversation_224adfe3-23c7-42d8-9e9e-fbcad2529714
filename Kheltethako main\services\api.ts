// Mock API service for KhelteThako app
// In a real application, these functions would call Supabase or another backend

// Type definitions
type Venue = {
  id: number;
  name: string;
  location: string;
  description: string;
  price_per_hour: number;
  rating: number;
  reviews_count: number;
  image_url: string;
  features: string[];
  capacity: number;
  is_featured?: boolean;
};

type VenueDetails = Venue & {
  images: string[];
  description: string;
  amenities: string[];
  available_dates: string[];
  time_slots: string[];
};

type Coach = {
  id: number;
  name: string;
  specialty: string;
  rating: number;
  reviews_count: number;
  price_per_session: number;
  image_url: string;
};

type Booking = {
  id: number;
  type: 'venue' | 'coach';
  name: string;
  location: string;
  date: string;
  start_time: string;
  end_time: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  total_price: number;
  image_url: string;
};

// Mock data - In a real app, this would come from Supabase
const VENUES: Venue[] = [
  {
    id: 1,
    name: 'Dhaka Cricket Academy',
    location: 'Mirpur, Dhaka',
    description: 'A premier cricket facility with international standard pitches and practice nets.',
    price_per_hour: 2500,
    rating: 4.8,
    reviews_count: 124,
    image_url: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    features: ['Cricket', 'Outdoor', 'Equipment Rental'],
    capacity: 22,
    is_featured: true,
  },
  {
    id: 2,
    name: 'Goal Football Arena',
    location: 'Banani, Dhaka',
    description: 'Indoor football arena with synthetic turf and air conditioning.',
    price_per_hour: 3000,
    rating: 4.5,
    reviews_count: 89,
    image_url: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    features: ['Football', 'Indoor', 'Air Conditioned'],
    capacity: 12,
  },
  {
    id: 3,
    name: 'SmashPoint Badminton Court',
    location: 'Gulshan, Dhaka',
    description: 'Professional badminton courts with proper lighting and wooden flooring.',
    price_per_hour: 1500,
    rating: 4.7,
    reviews_count: 56,
    image_url: 'https://images.pexels.com/photos/6540714/pexels-photo-6540714.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    features: ['Badminton', 'Indoor', 'Air Conditioned'],
    capacity: 4,
  },
  {
    id: 4,
    name: 'Swish Basketball Court',
    location: 'Dhanmondi, Dhaka',
    description: 'Full-size basketball court with adjustable hoops and scoreboards.',
    price_per_hour: 2000,
    rating: 4.3,
    reviews_count: 42,
    image_url: 'https://images.pexels.com/photos/1752757/pexels-photo-1752757.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    features: ['Basketball', 'Outdoor', 'Lighting'],
    capacity: 10,
  },
  {
    id: 5,
    name: 'Aqua Swimming Complex',
    location: 'Uttara, Dhaka',
    description: 'Olympic-size swimming pool with temperature control and professional staff.',
    price_per_hour: 1000,
    rating: 4.6,
    reviews_count: 78,
    image_url: 'https://images.pexels.com/photos/260598/pexels-photo-260598.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    features: ['Swimming', 'Indoor', 'Temperature Controlled'],
    capacity: 30,
  },
];

const COACHES: Coach[] = [
  {
    id: 1,
    name: 'Mashrafe Mortaza',
    specialty: 'Cricket Bowling',
    rating: 4.9,
    reviews_count: 87,
    price_per_session: 5000,
    image_url: 'https://images.pexels.com/photos/6551155/pexels-photo-6551155.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 2,
    name: 'Jamal Bhuyan',
    specialty: 'Football Midfielder',
    rating: 4.7,
    reviews_count: 54,
    price_per_session: 4500,
    image_url: 'https://images.pexels.com/photos/6638814/pexels-photo-6638814.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 3,
    name: 'Sabina Khatun',
    specialty: 'Football Forward',
    rating: 4.8,
    reviews_count: 42,
    price_per_session: 4000,
    image_url: 'https://images.pexels.com/photos/6765164/pexels-photo-6765164.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 4,
    name: 'Mohammad Rubel',
    specialty: 'Cricket Fast Bowling',
    rating: 4.6,
    reviews_count: 38,
    price_per_session: 4200,
    image_url: 'https://images.pexels.com/photos/6551137/pexels-photo-6551137.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
];

const BOOKINGS: Booking[] = [
  {
    id: 1,
    type: 'venue',
    name: 'Dhaka Cricket Academy',
    location: 'Mirpur, Dhaka',
    date: '2025-05-10',
    start_time: '14:00',
    end_time: '16:00',
    status: 'confirmed',
    total_price: 5000,
    image_url: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 2,
    type: 'coach',
    name: 'Mashrafe Mortaza',
    location: 'Mirpur Cricket Stadium',
    date: '2025-05-15',
    start_time: '10:00',
    end_time: '12:00',
    status: 'pending',
    total_price: 5000,
    image_url: 'https://images.pexels.com/photos/6551155/pexels-photo-6551155.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 3,
    type: 'venue',
    name: 'Goal Football Arena',
    location: 'Banani, Dhaka',
    date: '2025-04-20',
    start_time: '18:00',
    end_time: '20:00',
    status: 'confirmed',
    total_price: 6000,
    image_url: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
  {
    id: 4,
    type: 'venue',
    name: 'SmashPoint Badminton Court',
    location: 'Gulshan, Dhaka',
    date: '2025-03-15',
    start_time: '16:00',
    end_time: '18:00',
    status: 'cancelled',
    total_price: 3000,
    image_url: 'https://images.pexels.com/photos/6540714/pexels-photo-6540714.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
  },
];

// API functions with artificial delay to simulate network requests
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Get popular venues
export const getPopularVenues = async (categoryId?: string | null): Promise<Venue[]> => {
  await delay(1000);
  
  if (categoryId) {
    return VENUES.filter(venue => 
      venue.features.some(feature => 
        feature.toLowerCase() === categoryId.toLowerCase()
      )
    );
  }
  
  return VENUES;
};

// Get recommended coaches
export const getRecommendedCoaches = async (categoryId?: string | null): Promise<Coach[]> => {
  await delay(1000);
  
  if (categoryId === 'cricket') {
    return COACHES.filter(coach => coach.specialty.toLowerCase().includes('cricket'));
  } else if (categoryId === 'football') {
    return COACHES.filter(coach => coach.specialty.toLowerCase().includes('football'));
  }
  
  return COACHES;
};

// Search venues
export const searchVenues = async (
  query: string,
  filters: string[],
  sortBy: string
): Promise<Venue[]> => {
  await delay(1000);
  
  let results = [...VENUES];
  
  // Apply search query
  if (query) {
    const lowerQuery = query.toLowerCase();
    results = results.filter(
      venue => 
        venue.name.toLowerCase().includes(lowerQuery) ||
        venue.location.toLowerCase().includes(lowerQuery) ||
        venue.features.some(feature => feature.toLowerCase().includes(lowerQuery))
    );
  }
  
  // Apply filters
  if (filters.length > 0) {
    results = results.filter(venue => 
      filters.some(filter => 
        venue.features.some(feature => 
          feature.toLowerCase() === filter.toLowerCase()
        )
      )
    );
  }
  
  // Apply sorting
  if (sortBy === 'price_low') {
    results.sort((a, b) => a.price_per_hour - b.price_per_hour);
  } else if (sortBy === 'price_high') {
    results.sort((a, b) => b.price_per_hour - a.price_per_hour);
  } else if (sortBy === 'rating') {
    results.sort((a, b) => b.rating - a.rating);
  }
  
  return results;
};

// Get venue details
export const getVenueDetails = async (id: string | string[]): Promise<VenueDetails | null> => {
  await delay(1000);
  
  const venueId = typeof id === 'string' ? parseInt(id) : parseInt(id[0]);
  const venue = VENUES.find(v => v.id === venueId);
  
  if (!venue) return null;
  
  // Add detailed information
  return {
    ...venue,
    images: [
      venue.image_url,
      'https://images.pexels.com/photos/63249/california-road-highway-mountains-63249.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      'https://images.pexels.com/photos/270085/pexels-photo-270085.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    ],
    amenities: [
      'Changing Rooms',
      'Drinking Water',
      'Parking',
      'Equipment Rental',
      'Refreshments',
      'WiFi',
      'First Aid',
      'Coach Available',
    ],
    available_dates: [
      '2025-05-10',
      '2025-05-11',
      '2025-05-12',
      '2025-05-13',
      '2025-05-14',
      '2025-05-15',
    ],
    time_slots: [
      '08:00 - 10:00',
      '10:00 - 12:00',
      '12:00 - 14:00',
      '14:00 - 16:00',
      '16:00 - 18:00',
      '18:00 - 20:00',
      '20:00 - 22:00',
    ],
  };
};

// Get user bookings
export const getUserBookings = async (type: string): Promise<Booking[]> => {
  await delay(1000);
  
  const currentDate = new Date();
  
  if (type === 'upcoming') {
    return BOOKINGS.filter(booking => {
      const bookingDate = new Date(booking.date);
      return bookingDate >= currentDate && booking.status !== 'cancelled';
    });
  } else if (type === 'past') {
    return BOOKINGS.filter(booking => {
      const bookingDate = new Date(booking.date);
      return bookingDate < currentDate && booking.status !== 'cancelled';
    });
  } else if (type === 'cancelled') {
    return BOOKINGS.filter(booking => booking.status === 'cancelled');
  }
  
  return BOOKINGS;
};