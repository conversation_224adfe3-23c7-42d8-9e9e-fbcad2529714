// Test script to verify API is working
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://iymmfpbcawwzxloaovmm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTgyNzUsImV4cCI6MjA2NDE3NDI3NX0.nwhd7PPfGz9m5MCyah0RcLc_7XXnpEg47uPRjLyKi2Y';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAPI() {
  console.log('🧪 Testing Supabase API connection...\n');

  try {
    // Test 1: Fetch venues
    console.log('1️⃣ Testing venues...');
    const { data: venues, error: venuesError } = await supabase
      .from('venues')
      .select(`
        *,
        sports (
          name,
          icon
        )
      `)
      .eq('is_active', true)
      .eq('is_verified', true)
      .limit(3);

    if (venuesError) {
      console.error('❌ Venues error:', venuesError);
    } else {
      console.log(`✅ Found ${venues.length} venues:`);
      venues.forEach(venue => {
        console.log(`   - ${venue.name} (${venue.sports?.name}) - ৳${venue.price_per_hour}/hr`);
      });
    }

    // Test 2: Fetch coaches
    console.log('\n2️⃣ Testing coaches...');
    const { data: coaches, error: coachesError } = await supabase
      .from('coaches')
      .select(`
        *,
        users (
          full_name,
          email,
          phone
        ),
        sports (
          name,
          icon
        )
      `)
      .eq('is_active', true)
      .eq('is_verified', true)
      .limit(3);

    if (coachesError) {
      console.error('❌ Coaches error:', coachesError);
    } else {
      console.log(`✅ Found ${coaches.length} coaches:`);
      coaches.forEach(coach => {
        console.log(`   - ${coach.users?.full_name} (${coach.sports?.name}) - ৳${coach.hourly_rate}/hr`);
      });
    }

    // Test 3: Fetch available time slots
    console.log('\n3️⃣ Testing available time slots...');
    const { data: timeSlots, error: timeSlotsError } = await supabase
      .from('available_time_slots')
      .select('*')
      .eq('is_available', true)
      .gte('date', new Date().toISOString().split('T')[0])
      .limit(5);

    if (timeSlotsError) {
      console.error('❌ Time slots error:', timeSlotsError);
    } else {
      console.log(`✅ Found ${timeSlots.length} available time slots:`);
      timeSlots.forEach(slot => {
        const type = slot.venue_id ? `Venue ${slot.venue_id}` : `Coach ${slot.coach_id}`;
        console.log(`   - ${type}: ${slot.date} ${slot.start_time}-${slot.end_time} (৳${slot.price})`);
      });
    }

    // Test 4: Fetch sports
    console.log('\n4️⃣ Testing sports...');
    const { data: sports, error: sportsError } = await supabase
      .from('sports')
      .select('*')
      .limit(5);

    if (sportsError) {
      console.error('❌ Sports error:', sportsError);
    } else {
      console.log(`✅ Found ${sports.length} sports:`);
      sports.forEach(sport => {
        console.log(`   - ${sport.name} ${sport.icon}`);
      });
    }

    console.log('\n🎉 API test completed successfully!');
    console.log('\n📱 The app should now show real data from Supabase.');
    console.log('🔗 Open http://localhost:8082 to test the app.');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

testAPI();
