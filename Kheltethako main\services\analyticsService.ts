// Analytics Service for KhelteThako
import { supabase } from '@/lib/supabase';

export interface AnalyticsEvent {
  event_type: 'page_view' | 'booking_started' | 'booking_completed' | 'search' | 'favorite_added' | 'review_submitted' | 'user_signup' | 'user_login';
  user_id?: string;
  session_id?: string;
  page_url?: string;
  venue_id?: string;
  coach_id?: string;
  search_query?: string;
  metadata?: Record<string, any>;
}

/**
 * Generate a unique session ID for analytics tracking
 */
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Get or create session ID from storage
 */
export const getSessionId = (): string => {
  try {
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('analytics_session_id');
      if (!sessionId) {
        sessionId = generateSessionId();
        sessionStorage.setItem('analytics_session_id', sessionId);
      }
      return sessionId;
    }
    return generateSessionId();
  } catch (error) {
    console.warn('Error accessing sessionStorage:', error);
    return generateSessionId();
  }
};

/**
 * Track analytics event via Supabase Edge Function
 */
export const trackEvent = async (event: AnalyticsEvent): Promise<boolean> => {
  try {
    const { data, error } = await supabase.functions.invoke('analytics', {
      body: {
        ...event,
        session_id: event.session_id || getSessionId(),
        page_url: event.page_url || (typeof window !== 'undefined' ? window.location.href : undefined),
      }
    });

    if (error) {
      console.error('Error tracking analytics event:', error);
      return false;
    }

    console.log('Analytics event tracked:', event.event_type);
    return true;
  } catch (error) {
    console.error('Error in trackEvent:', error);
    return false;
  }
};

/**
 * Track page view
 */
export const trackPageView = async (
  pageUrl?: string,
  userId?: string,
  venueId?: string,
  coachId?: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'page_view',
    user_id: userId,
    page_url: pageUrl,
    venue_id: venueId,
    coach_id: coachId,
    metadata
  });
};

/**
 * Track search event
 */
export const trackSearch = async (
  searchQuery: string,
  userId?: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'search',
    user_id: userId,
    search_query: searchQuery,
    metadata: {
      ...metadata,
      query_length: searchQuery.length,
      has_filters: metadata?.filters ? Object.keys(metadata.filters).length > 0 : false
    }
  });
};

/**
 * Track booking started
 */
export const trackBookingStarted = async (
  userId: string,
  venueId?: string,
  coachId?: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'booking_started',
    user_id: userId,
    venue_id: venueId,
    coach_id: coachId,
    metadata: {
      ...metadata,
      booking_type: venueId ? 'venue' : 'coach'
    }
  });
};

/**
 * Track booking completed
 */
export const trackBookingCompleted = async (
  userId: string,
  venueId?: string,
  coachId?: string,
  bookingId?: string,
  amount?: number,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'booking_completed',
    user_id: userId,
    venue_id: venueId,
    coach_id: coachId,
    metadata: {
      ...metadata,
      booking_id: bookingId,
      amount: amount,
      booking_type: venueId ? 'venue' : 'coach'
    }
  });
};

/**
 * Track favorite added
 */
export const trackFavoriteAdded = async (
  userId: string,
  venueId?: string,
  coachId?: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'favorite_added',
    user_id: userId,
    venue_id: venueId,
    coach_id: coachId,
    metadata: {
      ...metadata,
      favorite_type: venueId ? 'venue' : 'coach'
    }
  });
};

/**
 * Track review submitted
 */
export const trackReviewSubmitted = async (
  userId: string,
  rating: number,
  venueId?: string,
  coachId?: string,
  bookingId?: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'review_submitted',
    user_id: userId,
    venue_id: venueId,
    coach_id: coachId,
    metadata: {
      ...metadata,
      rating: rating,
      booking_id: bookingId,
      review_type: venueId ? 'venue' : 'coach'
    }
  });
};

/**
 * Track user signup
 */
export const trackUserSignup = async (
  userId: string,
  signupMethod: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'user_signup',
    user_id: userId,
    metadata: {
      ...metadata,
      signup_method: signupMethod
    }
  });
};

/**
 * Track user login
 */
export const trackUserLogin = async (
  userId: string,
  loginMethod: string,
  metadata?: Record<string, any>
): Promise<boolean> => {
  return trackEvent({
    event_type: 'user_login',
    user_id: userId,
    metadata: {
      ...metadata,
      login_method: loginMethod
    }
  });
};

/**
 * Get analytics summary (admin only)
 */
export const getAnalyticsSummary = async (
  startDate?: string,
  endDate?: string
): Promise<any> => {
  try {
    let query = supabase
      .from('analytics_summary')
      .select('*')
      .order('event_date', { ascending: false });

    if (startDate) {
      query = query.gte('event_date', startDate);
    }

    if (endDate) {
      query = query.lte('event_date', endDate);
    }

    const { data, error } = await query.limit(100);

    if (error) {
      console.error('Error fetching analytics summary:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getAnalyticsSummary:', error);
    return null;
  }
};

export default {
  trackEvent,
  trackPageView,
  trackSearch,
  trackBookingStarted,
  trackBookingCompleted,
  trackFavoriteAdded,
  trackReviewSubmitted,
  trackUserSignup,
  trackUserLogin,
  getAnalyticsSummary,
  generateSessionId,
  getSessionId,
};
