// Push Notification Service - Supabase Edge Function
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
}

interface PushNotificationPayload {
  user_id?: string
  title: string
  body: string
  data?: any
  platform?: 'ios' | 'android' | 'web' | 'all'
  scheduled_at?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    if (req.method === 'POST') {
      // Send push notification
      const payload: PushNotificationPayload = await req.json()
      const result = await sendPushNotification(supabase, payload)
      
      return new Response(
        JSON.stringify(result),
        { 
          status: result.success ? 200 : 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else if (req.method === 'GET') {
      // Process notification queue
      const result = await processNotificationQueue(supabase)
      
      return new Response(
        JSON.stringify(result),
        { 
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Push notification error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function sendPushNotification(supabase: any, payload: PushNotificationPayload) {
  try {
    // If user_id is provided, send to specific user
    if (payload.user_id) {
      return await sendToUser(supabase, payload.user_id, payload)
    } else {
      // Send to all users (broadcast)
      return await sendBroadcast(supabase, payload)
    }
  } catch (error) {
    console.error('Error sending push notification:', error)
    return { success: false, error: error.message }
  }
}

async function sendToUser(supabase: any, userId: string, payload: PushNotificationPayload) {
  // Get user's push tokens
  const { data: tokens, error: tokenError } = await supabase
    .from('push_notification_tokens')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)

  if (tokenError) {
    throw new Error(`Error fetching push tokens: ${tokenError.message}`)
  }

  if (!tokens || tokens.length === 0) {
    return { success: false, error: 'No active push tokens found for user' }
  }

  // Filter tokens by platform if specified
  const filteredTokens = payload.platform && payload.platform !== 'all' 
    ? tokens.filter(token => token.platform === payload.platform)
    : tokens

  if (filteredTokens.length === 0) {
    return { success: false, error: `No tokens found for platform: ${payload.platform}` }
  }

  // Send notifications to each token
  const results = await Promise.allSettled(
    filteredTokens.map(token => sendToToken(token.token, token.platform, payload))
  )

  const successful = results.filter(result => result.status === 'fulfilled').length
  const failed = results.length - successful

  // Update token status for failed deliveries
  const failedTokens = results
    .map((result, index) => ({ result, token: filteredTokens[index] }))
    .filter(({ result }) => result.status === 'rejected')
    .map(({ token }) => token.token)

  if (failedTokens.length > 0) {
    await supabase
      .from('push_notification_tokens')
      .update({ is_active: false })
      .in('token', failedTokens)
  }

  return {
    success: successful > 0,
    sent: successful,
    failed: failed,
    total: results.length
  }
}

async function sendBroadcast(supabase: any, payload: PushNotificationPayload) {
  // Get all active push tokens
  const { data: tokens, error: tokenError } = await supabase
    .from('push_notification_tokens')
    .select('*')
    .eq('is_active', true)

  if (tokenError) {
    throw new Error(`Error fetching push tokens: ${tokenError.message}`)
  }

  if (!tokens || tokens.length === 0) {
    return { success: false, error: 'No active push tokens found' }
  }

  // Filter tokens by platform if specified
  const filteredTokens = payload.platform && payload.platform !== 'all' 
    ? tokens.filter(token => token.platform === payload.platform)
    : tokens

  // Send in batches to avoid overwhelming the service
  const batchSize = 100
  const batches = []
  for (let i = 0; i < filteredTokens.length; i += batchSize) {
    batches.push(filteredTokens.slice(i, i + batchSize))
  }

  let totalSent = 0
  let totalFailed = 0

  for (const batch of batches) {
    const results = await Promise.allSettled(
      batch.map(token => sendToToken(token.token, token.platform, payload))
    )

    const successful = results.filter(result => result.status === 'fulfilled').length
    const failed = results.length - successful

    totalSent += successful
    totalFailed += failed

    // Update failed tokens
    const failedTokens = results
      .map((result, index) => ({ result, token: batch[index] }))
      .filter(({ result }) => result.status === 'rejected')
      .map(({ token }) => token.token)

    if (failedTokens.length > 0) {
      await supabase
        .from('push_notification_tokens')
        .update({ is_active: false })
        .in('token', failedTokens)
    }
  }

  return {
    success: totalSent > 0,
    sent: totalSent,
    failed: totalFailed,
    total: filteredTokens.length
  }
}

async function sendToToken(token: string, platform: string, payload: PushNotificationPayload) {
  const fcmServerKey = Deno.env.get('FCM_SERVER_KEY')
  
  if (!fcmServerKey) {
    throw new Error('FCM_SERVER_KEY not configured')
  }

  const message = {
    to: token,
    notification: {
      title: payload.title,
      body: payload.body,
      icon: '/icon-192x192.png',
      badge: '/icon-192x192.png'
    },
    data: payload.data || {},
    priority: 'high',
    content_available: true
  }

  const response = await fetch('https://fcm.googleapis.com/fcm/send', {
    method: 'POST',
    headers: {
      'Authorization': `key=${fcmServerKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(message)
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`FCM error: ${error}`)
  }

  const result = await response.json()
  
  if (result.failure === 1) {
    throw new Error(`FCM delivery failed: ${JSON.stringify(result)}`)
  }

  return result
}

async function processNotificationQueue(supabase: any) {
  try {
    // Get pending notifications
    const { data: notifications, error: fetchError } = await supabase
      .from('notification_queue')
      .select('*')
      .eq('status', 'pending')
      .lte('scheduled_at', new Date().toISOString())
      .order('scheduled_at', { ascending: true })
      .limit(50)

    if (fetchError) {
      throw new Error(`Error fetching notifications: ${fetchError.message}`)
    }

    if (!notifications || notifications.length === 0) {
      return { success: true, processed: 0, message: 'No pending notifications' }
    }

    let processed = 0
    let failed = 0

    for (const notification of notifications) {
      try {
        // Mark as processing
        await supabase
          .from('notification_queue')
          .update({ status: 'processing' })
          .eq('id', notification.id)

        // Send notification
        const result = await sendToUser(supabase, notification.user_id, {
          title: notification.title,
          body: notification.body,
          data: notification.data,
          platform: notification.platform
        })

        if (result.success) {
          // Mark as sent
          await supabase
            .from('notification_queue')
            .update({ 
              status: 'sent',
              sent_at: new Date().toISOString()
            })
            .eq('id', notification.id)
          
          processed++
        } else {
          throw new Error(result.error)
        }

      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error)
        
        // Update retry count
        const newRetryCount = (notification.retry_count || 0) + 1
        const maxRetries = notification.max_retries || 3
        
        if (newRetryCount >= maxRetries) {
          // Mark as failed
          await supabase
            .from('notification_queue')
            .update({ 
              status: 'failed',
              error_message: error.message,
              retry_count: newRetryCount
            })
            .eq('id', notification.id)
        } else {
          // Schedule retry
          const retryAt = new Date(Date.now() + (newRetryCount * 60000)) // Exponential backoff
          await supabase
            .from('notification_queue')
            .update({ 
              status: 'pending',
              error_message: error.message,
              retry_count: newRetryCount,
              scheduled_at: retryAt.toISOString()
            })
            .eq('id', notification.id)
        }
        
        failed++
      }
    }

    return {
      success: true,
      processed,
      failed,
      total: notifications.length
    }

  } catch (error) {
    console.error('Error processing notification queue:', error)
    return { success: false, error: error.message }
  }
}
