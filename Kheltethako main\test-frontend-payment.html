<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test UddoktaPay Frontend Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #0B8457;
            margin-top: 0;
        }
        button {
            background-color: #0B8457;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0a7249;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .payment-method {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .payment-method:hover {
            border-color: #0B8457;
            background-color: #f8fff9;
        }
        .payment-method.bkash { border-color: #E91E63; }
        .payment-method.nagad { border-color: #FF6B35; }
        .payment-method.rocket { border-color: #8E44AD; }
        .payment-method.upay { border-color: #2ECC71; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 KhelteThako Payment Integration Test</h1>
        <p>This page tests the UddoktaPay integration without the React infinite render issues.</p>

        <div class="test-section">
            <h3>1. Test UddoktaPay API Connection</h3>
            <button onclick="testAPIConnection()">Test API Connection</button>
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Simulate Booking Creation</h3>
            <p>This simulates creating a booking and initializing payment:</p>
            <button onclick="simulateBooking()">Create Test Booking</button>
            <div id="booking-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Payment Methods</h3>
            <p>Click any payment method to test UddoktaPay integration:</p>
            <div class="payment-methods">
                <div class="payment-method bkash" onclick="testPayment('bKash')">
                    <h4>📱 bKash</h4>
                    <p>Mobile Banking</p>
                </div>
                <div class="payment-method nagad" onclick="testPayment('Nagad')">
                    <h4>📱 Nagad</h4>
                    <p>Mobile Banking</p>
                </div>
                <div class="payment-method rocket" onclick="testPayment('Rocket')">
                    <h4>📱 Rocket</h4>
                    <p>Mobile Banking</p>
                </div>
                <div class="payment-method upay" onclick="testPayment('Upay')">
                    <h4>📱 Upay</h4>
                    <p>Mobile Banking</p>
                </div>
            </div>
            <div id="payment-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Test Payment Verification</h3>
            <p>Enter an invoice ID to test payment verification:</p>
            <input type="text" id="invoice-id" placeholder="Enter Invoice ID" style="padding: 10px; width: 300px; margin-right: 10px;">
            <button onclick="testVerification()">Verify Payment</button>
            <div id="verify-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const UDDOKTAPAY_API_KEY = 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU';
        const UDDOKTAPAY_BASE_URL = 'https://digitaldot.paymently.io/api';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        async function testAPIConnection() {
            showResult('api-result', 'Testing API connection...', 'info');
            
            try {
                const response = await fetch(`${UDDOKTAPAY_BASE_URL}/checkout-v2`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
                    },
                    body: JSON.stringify({
                        full_name: 'Test User',
                        email: '<EMAIL>',
                        amount: 100,
                        payment_type: 'no-emi',
                        redirect_url: 'http://localhost:8082/payment-success',
                        cancel_url: 'http://localhost:8082/payment-cancel',
                        webhook_url: 'https://webhook.site/unique-id',
                    }),
                });

                const data = await response.json();
                
                if (response.ok && data.status) {
                    showResult('api-result', `✅ API Connection Successful!\n\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('api-result', `❌ API Error: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ Connection Failed: ${error.message}`, 'error');
            }
        }

        async function simulateBooking() {
            showResult('booking-result', 'Creating test booking...', 'info');
            
            // Simulate booking data
            const bookingData = {
                venue_name: 'Green Valley Football Club',
                date: new Date().toISOString().split('T')[0],
                time: '10:00 - 11:00',
                amount: 2000,
                user: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    phone: '+8801999999999'
                }
            };

            showResult('booking-result', `✅ Booking Created Successfully!\n\nBooking Details:\n${JSON.stringify(bookingData, null, 2)}\n\nReady for payment processing...`, 'success');
        }

        async function testPayment(method) {
            showResult('payment-result', `Initializing ${method} payment...`, 'info');
            
            try {
                const invoiceId = `KT_TEST_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
                
                const paymentData = {
                    full_name: 'Test User',
                    email: '<EMAIL>',
                    amount: 2000,
                    payment_type: 'no-emi',
                    redirect_url: `http://localhost:8082/payment-success?method=${method}&invoice_id=${invoiceId}`,
                    cancel_url: `http://localhost:8082/payment-cancel?method=${method}&invoice_id=${invoiceId}`,
                    webhook_url: 'https://webhook.site/unique-id',
                    metadata: {
                        payment_method: method,
                        test_payment: true,
                        app_name: 'KhelteThako'
                    }
                };

                const response = await fetch(`${UDDOKTAPAY_BASE_URL}/checkout-v2`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
                    },
                    body: JSON.stringify(paymentData),
                });

                const data = await response.json();
                
                if (response.ok && data.status && data.payment_url) {
                    showResult('payment-result', `✅ ${method} Payment Initialized!\n\nPayment URL: ${data.payment_url}\nInvoice ID: ${data.invoice_id || invoiceId}\n\nOpening payment page...`, 'success');
                    
                    // Open payment page in new tab
                    setTimeout(() => {
                        window.open(data.payment_url, '_blank');
                    }, 2000);
                } else {
                    showResult('payment-result', `❌ ${method} Payment Failed: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('payment-result', `❌ ${method} Payment Error: ${error.message}`, 'error');
            }
        }

        async function testVerification() {
            const invoiceId = document.getElementById('invoice-id').value.trim();
            
            if (!invoiceId) {
                showResult('verify-result', '❌ Please enter an Invoice ID', 'error');
                return;
            }

            showResult('verify-result', `Verifying payment for invoice: ${invoiceId}...`, 'info');
            
            try {
                const response = await fetch(`${UDDOKTAPAY_BASE_URL}/verify-payment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
                    },
                    body: JSON.stringify({
                        invoice_id: invoiceId
                    }),
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('verify-result', `✅ Verification Successful!\n\nStatus: ${data.status}\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('verify-result', `❌ Verification Failed: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('verify-result', `❌ Verification Error: ${error.message}`, 'error');
            }
        }

        // Auto-test API connection on page load
        window.onload = function() {
            console.log('🧪 KhelteThako Payment Test Page Loaded');
            console.log('✅ No React infinite render issues here!');
        };
    </script>
</body>
</html>
