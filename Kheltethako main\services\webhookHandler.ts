// UddoktaPay Webhook Handler for KhelteThako
// This would typically be implemented on your backend server
// This is a mock implementation for demonstration

import { verifyPayment, parsePaymentMetadata } from './uddoktapayApi';
import { updateBookingStatus, createNotification } from './supabaseApi';

export interface WebhookPayload {
  invoice_id: string;
  status: 'COMPLETED' | 'PENDING' | 'ERROR';
  amount: string;
  fee: string;
  charged_amount: string;
  payment_method: string;
  sender_number: string;
  transaction_id: string;
  date: string;
  metadata: any;
}

/**
 * Process UddoktaPay webhook notification
 * This should be called from your backend webhook endpoint
 */
export const processWebhook = async (payload: WebhookPayload) => {
  try {
    console.log('Processing UddoktaPay webhook:', payload);

    // Verify the payment with UddoktaPay API to ensure authenticity
    const verificationResult = await verifyPayment({ invoice_id: payload.invoice_id });
    
    if (!verificationResult) {
      throw new Error('Payment verification failed');
    }

    // Parse metadata to get booking information
    const metadata = parsePaymentMetadata(payload.metadata);
    
    if (!metadata.bookingId) {
      throw new Error('No booking ID found in payment metadata');
    }

    // Process based on payment status
    switch (payload.status) {
      case 'COMPLETED':
        await handleSuccessfulPayment(payload, metadata);
        break;
      case 'PENDING':
        await handlePendingPayment(payload, metadata);
        break;
      case 'ERROR':
        await handleFailedPayment(payload, metadata);
        break;
      default:
        console.warn('Unknown payment status:', payload.status);
    }

    return { success: true, message: 'Webhook processed successfully' };
  } catch (error) {
    console.error('Webhook processing error:', error);
    return { success: false, message: error.message };
  }
};

/**
 * Handle successful payment
 */
const handleSuccessfulPayment = async (payload: WebhookPayload, metadata: any) => {
  try {
    // Update booking status to confirmed
    await updateBookingStatus(metadata.bookingId, 'confirmed', {
      payment_id: payload.invoice_id,
      transaction_id: payload.transaction_id,
      payment_method: payload.payment_method,
      paid_amount: parseFloat(payload.charged_amount),
      payment_date: payload.date,
      payment_status: 'completed',
    });

    // Create success notification for user
    await createNotification({
      user_id: metadata.userId,
      title: 'Payment Successful',
      message: `Your booking payment of ৳${payload.charged_amount} has been confirmed. Transaction ID: ${payload.transaction_id}`,
      type: 'payment',
      action_url: `/booking/${metadata.bookingId}`,
    });

    // Send confirmation email (implement email service)
    await sendBookingConfirmationEmail(metadata.userId, metadata.bookingId, payload);

    console.log('Successful payment processed for booking:', metadata.bookingId);
  } catch (error) {
    console.error('Error handling successful payment:', error);
    throw error;
  }
};

/**
 * Handle pending payment
 */
const handlePendingPayment = async (payload: WebhookPayload, metadata: any) => {
  try {
    // Update booking status to pending payment
    await updateBookingStatus(metadata.bookingId, 'pending_payment', {
      payment_id: payload.invoice_id,
      transaction_id: payload.transaction_id,
      payment_method: payload.payment_method,
      payment_status: 'pending',
    });

    // Create pending notification for user
    await createNotification({
      user_id: metadata.userId,
      title: 'Payment Pending',
      message: `Your payment of ৳${payload.amount} is being processed. We'll notify you once it's confirmed.`,
      type: 'payment',
      action_url: `/booking/${metadata.bookingId}`,
    });

    console.log('Pending payment processed for booking:', metadata.bookingId);
  } catch (error) {
    console.error('Error handling pending payment:', error);
    throw error;
  }
};

/**
 * Handle failed payment
 */
const handleFailedPayment = async (payload: WebhookPayload, metadata: any) => {
  try {
    // Update booking status to payment failed
    await updateBookingStatus(metadata.bookingId, 'payment_failed', {
      payment_id: payload.invoice_id,
      transaction_id: payload.transaction_id,
      payment_method: payload.payment_method,
      payment_status: 'failed',
      failure_reason: 'Payment processing failed',
    });

    // Create failure notification for user
    await createNotification({
      user_id: metadata.userId,
      title: 'Payment Failed',
      message: `Your payment of ৳${payload.amount} could not be processed. Please try again or contact support.`,
      type: 'payment',
      action_url: `/booking/${metadata.bookingId}`,
    });

    console.log('Failed payment processed for booking:', metadata.bookingId);
  } catch (error) {
    console.error('Error handling failed payment:', error);
    throw error;
  }
};

/**
 * Send booking confirmation email
 * This would integrate with your email service (SendGrid, AWS SES, etc.)
 */
const sendBookingConfirmationEmail = async (userId: string, bookingId: string, paymentData: WebhookPayload) => {
  try {
    // Mock email sending - implement with your email service
    console.log('Sending confirmation email for booking:', bookingId);
    
    // Example email content
    const emailData = {
      to: userId, // This should be the user's email
      subject: 'Booking Confirmation - KhelteThako',
      template: 'booking-confirmation',
      data: {
        bookingId,
        transactionId: paymentData.transaction_id,
        amount: paymentData.charged_amount,
        paymentMethod: paymentData.payment_method,
        date: paymentData.date,
      },
    };

    // Implement actual email sending here
    // await emailService.send(emailData);
    
    console.log('Confirmation email sent successfully');
  } catch (error) {
    console.error('Error sending confirmation email:', error);
    // Don't throw error as email failure shouldn't fail the webhook
  }
};

/**
 * Validate webhook signature (implement based on UddoktaPay documentation)
 */
export const validateWebhookSignature = (payload: string, signature: string, secret: string): boolean => {
  try {
    // Implement signature validation based on UddoktaPay's webhook security
    // This is a mock implementation
    console.log('Validating webhook signature...');
    
    // Example validation (implement actual validation)
    // const expectedSignature = crypto.createHmac('sha256', secret).update(payload).digest('hex');
    // return expectedSignature === signature;
    
    return true; // Mock validation
  } catch (error) {
    console.error('Webhook signature validation error:', error);
    return false;
  }
};

/**
 * Express.js webhook endpoint example
 * This would be implemented on your backend server
 */
export const webhookEndpointExample = `
// Backend webhook endpoint (Express.js example)
app.post('/api/payment/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['uddoktapay-signature'];
    const payload = req.body.toString();
    
    // Validate webhook signature
    if (!validateWebhookSignature(payload, signature, process.env.UDDOKTAPAY_WEBHOOK_SECRET)) {
      return res.status(401).json({ error: 'Invalid signature' });
    }
    
    const webhookData = JSON.parse(payload);
    
    // Process webhook
    const result = await processWebhook(webhookData);
    
    if (result.success) {
      res.status(200).json({ message: 'Webhook processed successfully' });
    } else {
      res.status(400).json({ error: result.message });
    }
  } catch (error) {
    console.error('Webhook endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});
`;

export default {
  processWebhook,
  validateWebhookSignature,
  webhookEndpointExample,
};
