// UddoktaPay API Integration for KhelteThako
// Documentation: https://uddoktapay.readme.io/reference

const UDDOKTAPAY_CONFIG = {
  // Production URLs (replace with sandbox for testing)
  BASE_URL: 'https://digitaldot.paymently.io/api',
  CHECKOUT_URL: 'https://digitaldot.paymently.io/api/checkout-v2',
  VERIFY_URL: 'https://digitaldot.paymently.io/api/verify-payment',
  
  // Sandbox URLs for testing
  SANDBOX_BASE_URL: 'https://sandbox.uddoktapay.com/api',
  SANDBOX_CHECKOUT_URL: 'https://sandbox.uddoktapay.com/api/checkout-v2',
  SANDBOX_VERIFY_URL: 'https://sandbox.uddoktapay.com/api/verify-payment',
  
  // API Key
  API_KEY: 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU',
  SANDBOX_API_KEY: '982d381360a69d419689740d9f2e26ce36fb7a50',
  
  // Environment
  IS_SANDBOX: __DEV__, // Use sandbox in development
};

// Get the appropriate URLs and API key based on environment
const getConfig = () => {
  if (UDDOKTAPAY_CONFIG.IS_SANDBOX) {
    return {
      checkoutUrl: UDDOKTAPAY_CONFIG.SANDBOX_CHECKOUT_URL,
      verifyUrl: UDDOKTAPAY_CONFIG.SANDBOX_VERIFY_URL,
      apiKey: UDDOKTAPAY_CONFIG.SANDBOX_API_KEY,
    };
  }
  return {
    checkoutUrl: UDDOKTAPAY_CONFIG.CHECKOUT_URL,
    verifyUrl: UDDOKTAPAY_CONFIG.VERIFY_URL,
    apiKey: UDDOKTAPAY_CONFIG.API_KEY,
  };
};

export interface CreateChargeRequest {
  full_name: string;
  email: string;
  amount: string;
  metadata: {
    user_id: string;
    booking_id?: string;
    venue_id?: string;
    coach_id?: string;
    booking_type: 'venue' | 'coach';
    booking_date: string;
    time_slot?: string;
  };
  redirect_url: string;
  cancel_url: string;
  webhook_url?: string;
  return_type?: 'GET' | 'POST';
}

export interface CreateChargeResponse {
  status: boolean;
  message: string;
  payment_url?: string;
}

export interface VerifyPaymentRequest {
  invoice_id: string;
}

export interface VerifyPaymentResponse {
  full_name: string;
  email: string;
  amount: string;
  fee: string;
  charged_amount: string;
  invoice_id: string;
  metadata: {
    user_id: string;
    booking_id?: string;
    venue_id?: string;
    coach_id?: string;
    booking_type: 'venue' | 'coach';
    booking_date: string;
    time_slot?: string;
  };
  payment_method: string;
  sender_number: string;
  transaction_id: string;
  date: string;
  status: 'COMPLETED' | 'PENDING' | 'ERROR';
}

/**
 * Create a payment charge with UddoktaPay
 */
export const createCharge = async (request: CreateChargeRequest): Promise<CreateChargeResponse> => {
  const config = getConfig();
  
  try {
    const response = await fetch(config.checkoutUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': config.apiKey,
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to create payment charge');
    }

    return data;
  } catch (error) {
    console.error('UddoktaPay Create Charge Error:', error);
    throw error;
  }
};

/**
 * Verify payment status with UddoktaPay
 */
export const verifyPayment = async (request: VerifyPaymentRequest): Promise<VerifyPaymentResponse> => {
  const config = getConfig();
  
  try {
    const response = await fetch(config.verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': config.apiKey,
      },
      body: JSON.stringify(request),
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Failed to verify payment');
    }

    return data;
  } catch (error) {
    console.error('UddoktaPay Verify Payment Error:', error);
    throw error;
  }
};

/**
 * Helper function to create a booking payment
 */
export const createBookingPayment = async (bookingData: {
  user: {
    id: string;
    full_name: string;
    email: string;
  };
  booking: {
    id?: string;
    venue_id?: string;
    coach_id?: string;
    type: 'venue' | 'coach';
    date: string;
    time_slot?: string;
    amount: number;
  };
  redirectUrls: {
    success: string;
    cancel: string;
    webhook?: string;
  };
}): Promise<CreateChargeResponse> => {
  const { user, booking, redirectUrls } = bookingData;
  
  const chargeRequest: CreateChargeRequest = {
    full_name: user.full_name,
    email: user.email,
    amount: booking.amount.toString(),
    metadata: {
      user_id: user.id,
      booking_id: booking.id,
      venue_id: booking.venue_id,
      coach_id: booking.coach_id,
      booking_type: booking.type,
      booking_date: booking.date,
      time_slot: booking.time_slot,
    },
    redirect_url: redirectUrls.success,
    cancel_url: redirectUrls.cancel,
    webhook_url: redirectUrls.webhook,
    return_type: 'GET', // Use GET for easier handling in React Native
  };

  return createCharge(chargeRequest);
};

/**
 * Process payment verification and update booking status
 */
export const processPaymentVerification = async (
  invoiceId: string,
  onSuccess?: (paymentData: VerifyPaymentResponse) => void,
  onError?: (error: string) => void
): Promise<VerifyPaymentResponse | null> => {
  try {
    const paymentData = await verifyPayment({ invoice_id: invoiceId });
    
    if (paymentData.status === 'COMPLETED') {
      // Payment successful
      if (onSuccess) {
        onSuccess(paymentData);
      }
      return paymentData;
    } else if (paymentData.status === 'PENDING') {
      // Payment pending
      if (onError) {
        onError('Payment is still pending. Please wait for confirmation.');
      }
      return paymentData;
    } else {
      // Payment failed
      if (onError) {
        onError('Payment failed. Please try again.');
      }
      return paymentData;
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    if (onError) {
      onError('Failed to verify payment. Please contact support.');
    }
    return null;
  }
};

/**
 * Generate redirect URLs for the app
 */
export const generateRedirectUrls = (bookingId: string) => {
  // In a real app, these would be your actual domain URLs
  // For development, you might use deep links or a local server
  const baseUrl = UDDOKTAPAY_CONFIG.IS_SANDBOX 
    ? 'https://your-app.com' // Replace with your actual domain
    : 'https://kheltethako.com'; // Replace with your production domain
  
  return {
    success: `${baseUrl}/payment/success?booking_id=${bookingId}`,
    cancel: `${baseUrl}/payment/cancel?booking_id=${bookingId}`,
    webhook: `${baseUrl}/api/payment/webhook`,
  };
};

/**
 * Format amount for UddoktaPay (should be string)
 */
export const formatAmount = (amount: number): string => {
  return amount.toFixed(2);
};

/**
 * Parse metadata from UddoktaPay response
 */
export const parsePaymentMetadata = (metadata: any) => {
  return {
    userId: metadata.user_id,
    bookingId: metadata.booking_id,
    venueId: metadata.venue_id,
    coachId: metadata.coach_id,
    bookingType: metadata.booking_type,
    bookingDate: metadata.booking_date,
    timeSlot: metadata.time_slot,
  };
};

export default {
  createCharge,
  verifyPayment,
  createBookingPayment,
  processPaymentVerification,
  generateRedirectUrls,
  formatAmount,
  parsePaymentMetadata,
};
