# KhelteThako Deployment Guide

## 🚀 Complete Backend Implementation with Supabase

This guide covers the deployment of the complete KhelteThako backend system including:
- **UddoktaPay Webhook Handler**
- **Push Notification Service**
- **Analytics Dashboard**
- **Database Schema**

## 📋 Prerequisites

1. **Supabase Project**: Active Supabase project
2. **UddoktaPay Account**: Production API credentials
3. **Firebase/FCM**: For push notifications
4. **Expo Account**: For push notification tokens

## 🗄️ Database Setup

### 1. Run Migrations

Execute the following SQL migrations in your Supabase SQL editor:

```bash
# Apply payment system schema
supabase db push

# Or manually run:
# - supabase/migrations/001_payment_system.sql
# - supabase/migrations/002_analytics_functions.sql
```

### 2. Verify Tables Created

Ensure these tables exist in your database:
- `payment_transactions`
- `webhook_events`
- `payment_analytics`
- `push_notification_tokens`
- `notification_queue`

## 🔧 Edge Functions Deployment

### 1. Deploy Webhook Handler

```bash
# Deploy UddoktaPay webhook function
supabase functions deploy uddoktapay-webhook

# Test the function
curl -X POST 'https://your-project.supabase.co/functions/v1/uddoktapay-webhook' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"test": "webhook"}'
```

### 2. Deploy Push Notification Service

```bash
# Deploy push notification function
supabase functions deploy push-notifications

# Test the function
curl -X POST 'https://your-project.supabase.co/functions/v1/push-notifications' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_id": "test-user-id",
    "title": "Test Notification",
    "body": "This is a test notification"
  }'
```

## 🔐 Environment Variables

### 1. Supabase Environment Variables

Set these in your Supabase project settings:

```bash
# Firebase Cloud Messaging
FCM_SERVER_KEY=your-fcm-server-key

# UddoktaPay Webhook Security
UDDOKTAPAY_WEBHOOK_SECRET=your-webhook-secret

# Supabase (automatically available)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 2. React Native App Environment

Update your app configuration:

```typescript
// In your .env or config file
EXPO_PROJECT_ID=your-expo-project-id
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

## 🔗 UddoktaPay Integration

### 1. Configure Webhook URL

In your UddoktaPay dashboard, set the webhook URL to:
```
https://your-project.supabase.co/functions/v1/uddoktapay-webhook
```

### 2. Update API Credentials

Update the UddoktaPay configuration in your app:

```typescript
// In services/uddoktapayApi.ts
const UDDOKTAPAY_CONFIG = {
  API_KEY: 'your-production-api-key',
  BASE_URL: 'https://digitaldot.paymently.io/api',
  // ... other config
};
```

## 📱 Push Notifications Setup

### 1. Firebase Configuration

1. Create a Firebase project
2. Add your Android/iOS apps
3. Download configuration files
4. Get the FCM Server Key from Project Settings > Cloud Messaging

### 2. Expo Configuration

Update your `app.json`:

```json
{
  "expo": {
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/notification-icon.png",
          "color": "#0B8457",
          "sounds": ["./assets/notification-sound.wav"]
        }
      ]
    ]
  }
}
```

## 📊 Analytics Dashboard

### 1. Access Analytics

The analytics dashboard is available at:
```
/admin/analytics
```

### 2. Key Metrics Available

- **Real-time Metrics**: Today's revenue, transactions, success rate
- **Payment Analytics**: Revenue trends, payment method breakdown
- **User Engagement**: New users, retention rates
- **Venue Performance**: Top performing venues by revenue
- **Export Functionality**: CSV export for all data types

## 🔄 Automated Processes

### 1. Webhook Processing

The webhook handler automatically:
- Validates UddoktaPay signatures
- Updates payment transaction status
- Updates booking status
- Sends push notifications
- Updates analytics data

### 2. Push Notification Queue

The notification service:
- Processes queued notifications
- Handles retry logic for failed deliveries
- Manages device token lifecycle
- Supports broadcast notifications

## 🧪 Testing

### 1. Test Webhook Handler

```bash
# Test with sample UddoktaPay payload
curl -X POST 'https://your-project.supabase.co/functions/v1/uddoktapay-webhook' \
  -H 'Content-Type: application/json' \
  -H 'uddoktapay-signature: test-signature' \
  -d '{
    "invoice_id": "test-invoice-123",
    "status": "COMPLETED",
    "amount": "2000.00",
    "fee": "100.00",
    "charged_amount": "2100.00",
    "payment_method": "bKash",
    "sender_number": "01712345678",
    "transaction_id": "TXN123456789",
    "date": "2024-01-15T10:30:00Z",
    "metadata": {
      "user_id": "user-123",
      "booking_id": "booking-456",
      "booking_type": "venue"
    }
  }'
```

### 2. Test Push Notifications

```bash
# Send test notification
curl -X POST 'https://your-project.supabase.co/functions/v1/push-notifications' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "user_id": "test-user-id",
    "title": "Booking Confirmed",
    "body": "Your venue booking has been confirmed!",
    "data": {
      "type": "booking",
      "booking_id": "booking-123"
    }
  }'
```

## 📈 Monitoring

### 1. Supabase Logs

Monitor your Edge Functions in the Supabase dashboard:
- Function invocations
- Error rates
- Response times
- Resource usage

### 2. Database Monitoring

Track key metrics:
- Payment transaction success rates
- Webhook processing times
- Push notification delivery rates
- User engagement metrics

## 🔒 Security Considerations

### 1. Webhook Security

- Validate UddoktaPay signatures
- Use HTTPS for all webhook endpoints
- Implement rate limiting
- Log all webhook events for audit

### 2. Push Notification Security

- Validate JWT tokens for authenticated endpoints
- Sanitize notification content
- Implement user consent for notifications
- Handle token lifecycle properly

## 🚀 Production Deployment

### 1. Final Checklist

- [ ] Database migrations applied
- [ ] Edge functions deployed
- [ ] Environment variables configured
- [ ] UddoktaPay webhook URL updated
- [ ] Firebase/FCM configured
- [ ] Analytics dashboard accessible
- [ ] All tests passing

### 2. Go Live

1. Update UddoktaPay to production mode
2. Configure production webhook URL
3. Test end-to-end payment flow
4. Monitor analytics dashboard
5. Verify push notifications working

## 📞 Support

For issues with:
- **UddoktaPay**: Contact UddoktaPay support
- **Supabase**: Check Supabase documentation
- **Firebase/FCM**: Refer to Firebase documentation
- **Expo**: Check Expo documentation

## 🎉 Success!

Your KhelteThako backend is now fully deployed with:
✅ **Automated Payment Processing**
✅ **Real-time Push Notifications**
✅ **Comprehensive Analytics**
✅ **Production-Ready Infrastructure**

The system will automatically handle payments, send notifications, and track analytics for your sports booking platform!
