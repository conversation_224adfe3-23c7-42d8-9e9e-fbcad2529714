// Test UddoktaPay integration
const { default: fetch } = require('node-fetch');

const UDDOKTAPAY_API_KEY = 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU';
const UDDOKTAPAY_BASE_URL = 'https://digitaldot.paymently.io/api';

async function testUddoktaPay() {
  console.log('🧪 Testing UddoktaPay API integration...\n');

  try {
    // Test payment creation
    console.log('1️⃣ Testing payment creation...');
    
    const paymentData = {
      full_name: 'Test User',
      email: '<EMAIL>',
      amount: 100,
      payment_type: 'no-emi',
      redirect_url: 'http://localhost:8082/payment-success?booking_id=123&invoice_id=TEST_INVOICE',
      cancel_url: 'http://localhost:8082/payment-cancel?booking_id=123&invoice_id=TEST_INVOICE',
      webhook_url: 'https://webhook.site/unique-id',
      metadata: {
        booking_id: '123',
        user_id: 'test-user',
        booking_type: 'venue',
        app_name: '<PERSON>hel<PERSON><PERSON><PERSON><PERSON>'
      }
    };

    console.log('Payment request data:', JSON.stringify(paymentData, null, 2));

    const response = await fetch(`${UDDOKTAPAY_BASE_URL}/checkout-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
      },
      body: JSON.stringify(paymentData),
    });

    const responseData = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(responseData, null, 2));

    if (response.ok && responseData.payment_url) {
      console.log('✅ Payment creation successful!');
      console.log('🔗 Payment URL:', responseData.payment_url);
      
      // Test payment verification (this will fail since we didn't actually pay)
      console.log('\n2️⃣ Testing payment verification...');
      
      const verifyResponse = await fetch(`${UDDOKTAPAY_BASE_URL}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
        },
        body: JSON.stringify({
          invoice_id: responseData.invoice_id
        }),
      });

      const verifyData = await verifyResponse.json();
      console.log('Verification response:', JSON.stringify(verifyData, null, 2));
      
      if (verifyResponse.ok) {
        console.log('✅ Payment verification API working!');
      } else {
        console.log('⚠️ Payment verification failed (expected for unpaid invoice)');
      }
      
    } else {
      console.log('❌ Payment creation failed');
      console.log('Error:', responseData);
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }

  console.log('\n🎯 Integration test completed!');
  console.log('📱 You can now test the payment flow in the app.');
}

testUddoktaPay();
