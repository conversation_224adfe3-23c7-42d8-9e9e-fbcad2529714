-- Analytics Functions for KhelteThako Payment System

-- Function to upsert payment analytics
CREATE OR REPLACE FUNCTION upsert_payment_analytics(
    p_date DATE,
    p_hour INTEGER,
    p_status VARCHAR,
    p_amount DECIMAL,
    p_fee DECIMAL,
    p_payment_method VARCHAR,
    p_booking_type VARCHAR
)
RETURNS VOID AS $$
DECLARE
    payment_method_col VARCHAR;
BEGIN
    -- Map payment method to column name
    payment_method_col := CASE 
        WHEN LOWER(p_payment_method) LIKE '%bkash%' THEN 'bkash_transactions'
        WHEN LOWER(p_payment_method) LIKE '%nagad%' THEN 'nagad_transactions'
        WHEN LOWER(p_payment_method) LIKE '%rocket%' THEN 'rocket_transactions'
        WHEN LOWER(p_payment_method) LIKE '%upay%' THEN 'upay_transactions'
        ELSE 'bank_transactions'
    END;

    -- Insert or update analytics record
    INSERT INTO payment_analytics (
        date, 
        hour, 
        day_of_week, 
        month, 
        year,
        total_transactions,
        successful_transactions,
        failed_transactions,
        cancelled_transactions,
        total_revenue,
        total_fees,
        net_revenue,
        bkash_transactions,
        nagad_transactions,
        rocket_transactions,
        upay_transactions,
        bank_transactions,
        venue_bookings,
        coach_bookings
    )
    VALUES (
        p_date,
        p_hour,
        EXTRACT(DOW FROM p_date),
        EXTRACT(MONTH FROM p_date),
        EXTRACT(YEAR FROM p_date),
        1, -- total_transactions
        CASE WHEN p_status = 'completed' THEN 1 ELSE 0 END,
        CASE WHEN p_status = 'failed' THEN 1 ELSE 0 END,
        CASE WHEN p_status = 'cancelled' THEN 1 ELSE 0 END,
        CASE WHEN p_status = 'completed' THEN p_amount ELSE 0 END,
        CASE WHEN p_status = 'completed' THEN p_fee ELSE 0 END,
        CASE WHEN p_status = 'completed' THEN (p_amount - p_fee) ELSE 0 END,
        CASE WHEN payment_method_col = 'bkash_transactions' THEN 1 ELSE 0 END,
        CASE WHEN payment_method_col = 'nagad_transactions' THEN 1 ELSE 0 END,
        CASE WHEN payment_method_col = 'rocket_transactions' THEN 1 ELSE 0 END,
        CASE WHEN payment_method_col = 'upay_transactions' THEN 1 ELSE 0 END,
        CASE WHEN payment_method_col = 'bank_transactions' THEN 1 ELSE 0 END,
        CASE WHEN p_booking_type = 'venue' THEN 1 ELSE 0 END,
        CASE WHEN p_booking_type = 'coach' THEN 1 ELSE 0 END
    )
    ON CONFLICT (date, hour)
    DO UPDATE SET
        total_transactions = payment_analytics.total_transactions + 1,
        successful_transactions = payment_analytics.successful_transactions + 
            CASE WHEN p_status = 'completed' THEN 1 ELSE 0 END,
        failed_transactions = payment_analytics.failed_transactions + 
            CASE WHEN p_status = 'failed' THEN 1 ELSE 0 END,
        cancelled_transactions = payment_analytics.cancelled_transactions + 
            CASE WHEN p_status = 'cancelled' THEN 1 ELSE 0 END,
        total_revenue = payment_analytics.total_revenue + 
            CASE WHEN p_status = 'completed' THEN p_amount ELSE 0 END,
        total_fees = payment_analytics.total_fees + 
            CASE WHEN p_status = 'completed' THEN p_fee ELSE 0 END,
        net_revenue = payment_analytics.net_revenue + 
            CASE WHEN p_status = 'completed' THEN (p_amount - p_fee) ELSE 0 END,
        bkash_transactions = payment_analytics.bkash_transactions + 
            CASE WHEN payment_method_col = 'bkash_transactions' THEN 1 ELSE 0 END,
        nagad_transactions = payment_analytics.nagad_transactions + 
            CASE WHEN payment_method_col = 'nagad_transactions' THEN 1 ELSE 0 END,
        rocket_transactions = payment_analytics.rocket_transactions + 
            CASE WHEN payment_method_col = 'rocket_transactions' THEN 1 ELSE 0 END,
        upay_transactions = payment_analytics.upay_transactions + 
            CASE WHEN payment_method_col = 'upay_transactions' THEN 1 ELSE 0 END,
        bank_transactions = payment_analytics.bank_transactions + 
            CASE WHEN payment_method_col = 'bank_transactions' THEN 1 ELSE 0 END,
        venue_bookings = payment_analytics.venue_bookings + 
            CASE WHEN p_booking_type = 'venue' THEN 1 ELSE 0 END,
        coach_bookings = payment_analytics.coach_bookings + 
            CASE WHEN p_booking_type = 'coach' THEN 1 ELSE 0 END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to get payment analytics summary
CREATE OR REPLACE FUNCTION get_payment_analytics_summary(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    total_transactions BIGINT,
    successful_transactions BIGINT,
    failed_transactions BIGINT,
    success_rate DECIMAL,
    total_revenue DECIMAL,
    total_fees DECIMAL,
    net_revenue DECIMAL,
    avg_transaction_value DECIMAL,
    top_payment_method VARCHAR,
    venue_vs_coach_ratio DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(pa.total_transactions)::BIGINT,
        SUM(pa.successful_transactions)::BIGINT,
        SUM(pa.failed_transactions)::BIGINT,
        CASE 
            WHEN SUM(pa.total_transactions) > 0 
            THEN ROUND((SUM(pa.successful_transactions)::DECIMAL / SUM(pa.total_transactions)) * 100, 2)
            ELSE 0
        END,
        SUM(pa.total_revenue),
        SUM(pa.total_fees),
        SUM(pa.net_revenue),
        CASE 
            WHEN SUM(pa.successful_transactions) > 0 
            THEN ROUND(SUM(pa.total_revenue) / SUM(pa.successful_transactions), 2)
            ELSE 0
        END,
        (
            SELECT method FROM (
                SELECT 'bKash' as method, SUM(pa.bkash_transactions) as count
                UNION ALL
                SELECT 'Nagad', SUM(pa.nagad_transactions)
                UNION ALL
                SELECT 'Rocket', SUM(pa.rocket_transactions)
                UNION ALL
                SELECT 'Upay', SUM(pa.upay_transactions)
                UNION ALL
                SELECT 'Bank', SUM(pa.bank_transactions)
            ) methods
            ORDER BY count DESC
            LIMIT 1
        ),
        CASE 
            WHEN SUM(pa.coach_bookings) > 0 
            THEN ROUND(SUM(pa.venue_bookings)::DECIMAL / SUM(pa.coach_bookings), 2)
            ELSE SUM(pa.venue_bookings)::DECIMAL
        END
    FROM payment_analytics pa
    WHERE pa.date BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get hourly payment trends
CREATE OR REPLACE FUNCTION get_hourly_payment_trends(
    target_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    hour INTEGER,
    transactions BIGINT,
    revenue DECIMAL,
    success_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pa.hour,
        pa.total_transactions::BIGINT,
        pa.total_revenue,
        CASE 
            WHEN pa.total_transactions > 0 
            THEN ROUND((pa.successful_transactions::DECIMAL / pa.total_transactions) * 100, 2)
            ELSE 0
        END
    FROM payment_analytics pa
    WHERE pa.date = target_date
    ORDER BY pa.hour;
END;
$$ LANGUAGE plpgsql;

-- Function to get payment method breakdown
CREATE OR REPLACE FUNCTION get_payment_method_breakdown(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    payment_method VARCHAR,
    transactions BIGINT,
    percentage DECIMAL
) AS $$
DECLARE
    total_transactions BIGINT;
BEGIN
    -- Get total transactions for percentage calculation
    SELECT SUM(pa.total_transactions) INTO total_transactions
    FROM payment_analytics pa
    WHERE pa.date BETWEEN start_date AND end_date;

    RETURN QUERY
    SELECT 
        method,
        count,
        CASE 
            WHEN total_transactions > 0 
            THEN ROUND((count::DECIMAL / total_transactions) * 100, 2)
            ELSE 0
        END
    FROM (
        SELECT 'bKash' as method, SUM(pa.bkash_transactions) as count
        FROM payment_analytics pa
        WHERE pa.date BETWEEN start_date AND end_date
        UNION ALL
        SELECT 'Nagad', SUM(pa.nagad_transactions)
        FROM payment_analytics pa
        WHERE pa.date BETWEEN start_date AND end_date
        UNION ALL
        SELECT 'Rocket', SUM(pa.rocket_transactions)
        FROM payment_analytics pa
        WHERE pa.date BETWEEN start_date AND end_date
        UNION ALL
        SELECT 'Upay', SUM(pa.upay_transactions)
        FROM payment_analytics pa
        WHERE pa.date BETWEEN start_date AND end_date
        UNION ALL
        SELECT 'Bank Transfer', SUM(pa.bank_transactions)
        FROM payment_analytics pa
        WHERE pa.date BETWEEN start_date AND end_date
    ) methods
    WHERE count > 0
    ORDER BY count DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get daily revenue trends
CREATE OR REPLACE FUNCTION get_daily_revenue_trends(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
    date DATE,
    total_revenue DECIMAL,
    net_revenue DECIMAL,
    transactions BIGINT,
    avg_transaction_value DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pa.date,
        SUM(pa.total_revenue),
        SUM(pa.net_revenue),
        SUM(pa.total_transactions)::BIGINT,
        CASE 
            WHEN SUM(pa.successful_transactions) > 0 
            THEN ROUND(SUM(pa.total_revenue) / SUM(pa.successful_transactions), 2)
            ELSE 0
        END
    FROM payment_analytics pa
    WHERE pa.date BETWEEN start_date AND end_date
    GROUP BY pa.date
    ORDER BY pa.date;
END;
$$ LANGUAGE plpgsql;
