import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  MessageCircle,
  Phone,
  Mail,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  Send,
} from 'lucide-react-native';

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <View style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <Text style={styles.faqQuestionText}>{question}</Text>
        {isExpanded ? (
          <ChevronDown size={16} color="#6B7280" />
        ) : (
          <ChevronRight size={16} color="#6B7280" />
        )}
      </TouchableOpacity>
      {isExpanded && (
        <View style={styles.faqAnswer}>
          <Text style={styles.faqAnswerText}>{answer}</Text>
        </View>
      )}
    </View>
  );
};

const faqs = [
  {
    question: 'How do I book a venue?',
    answer: 'To book a venue, browse through our available venues, select your preferred date and time, and complete the payment process. You will receive a confirmation email once your booking is confirmed.',
  },
  {
    question: 'Can I cancel my booking?',
    answer: 'Yes, you can cancel your booking up to 24 hours before the scheduled time for a full refund. Cancellations made within 24 hours will incur a 50% cancellation fee.',
  },
  {
    question: 'How do I contact a coach?',
    answer: 'You can contact coaches directly through their profile page using the contact button, or book a session with them through the app.',
  },
  {
    question: 'What payment methods are accepted?',
    answer: 'We accept all major credit cards, debit cards, and mobile banking services like bKash, Nagad, and Rocket.',
  },
  {
    question: 'How do I become a venue owner?',
    answer: 'To list your venue on KhelteThako, contact our support team or create a venue owner account. We will guide you through the verification process.',
  },
  {
    question: 'Is there a membership program?',
    answer: 'Yes, we offer premium memberships with benefits like priority booking, discounts, and exclusive access to premium venues.',
  },
];

export default function HelpScreen() {
  const [activeTab, setActiveTab] = useState('faq');
  const [message, setMessage] = useState('');

  const handleContactSupport = (method: string) => {
    switch (method) {
      case 'phone':
        Linking.openURL('tel:+*************');
        break;
      case 'email':
        Linking.openURL('mailto:<EMAIL>');
        break;
      case 'whatsapp':
        Linking.openURL('whatsapp://send?phone=*************');
        break;
    }
  };

  const handleSendMessage = () => {
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }

    Alert.alert(
      'Message Sent',
      'Thank you for contacting us! We will get back to you within 24 hours.',
      [
        {
          text: 'OK',
          onPress: () => {
            setMessage('');
            router.back();
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Help & Support</Text>
      </View>

      {/* Tab Selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'faq' && styles.activeTab]}
          onPress={() => setActiveTab('faq')}
        >
          <HelpCircle size={16} color={activeTab === 'faq' ? '#EF4444' : '#6B7280'} />
          <Text style={[styles.tabText, activeTab === 'faq' && styles.activeTabText]}>
            FAQ
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'contact' && styles.activeTab]}
          onPress={() => setActiveTab('contact')}
        >
          <MessageCircle size={16} color={activeTab === 'contact' ? '#EF4444' : '#6B7280'} />
          <Text style={[styles.tabText, activeTab === 'contact' && styles.activeTabText]}>
            Contact Us
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'faq' ? (
          <View style={styles.faqContainer}>
            <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
            {faqs.map((faq, index) => (
              <FAQItem key={index} question={faq.question} answer={faq.answer} />
            ))}
          </View>
        ) : (
          <View style={styles.contactContainer}>
            {/* Quick Contact Options */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Quick Contact</Text>
              <TouchableOpacity
                style={styles.contactOption}
                onPress={() => handleContactSupport('phone')}
              >
                <View style={styles.contactIcon}>
                  <Phone size={20} color="#EF4444" />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactTitle}>Call Us</Text>
                  <Text style={styles.contactSubtitle}>+880 1234-567890</Text>
                </View>
                <ChevronRight size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.contactOption}
                onPress={() => handleContactSupport('email')}
              >
                <View style={styles.contactIcon}>
                  <Mail size={20} color="#EF4444" />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactTitle}>Email Us</Text>
                  <Text style={styles.contactSubtitle}><EMAIL></Text>
                </View>
                <ChevronRight size={16} color="#9CA3AF" />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.contactOption}
                onPress={() => handleContactSupport('whatsapp')}
              >
                <View style={styles.contactIcon}>
                  <MessageCircle size={20} color="#EF4444" />
                </View>
                <View style={styles.contactInfo}>
                  <Text style={styles.contactTitle}>WhatsApp</Text>
                  <Text style={styles.contactSubtitle}>Chat with us instantly</Text>
                </View>
                <ChevronRight size={16} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            {/* Send Message */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Send us a message</Text>
              <View style={styles.messageContainer}>
                <TextInput
                  style={styles.messageInput}
                  value={message}
                  onChangeText={setMessage}
                  placeholder="Describe your issue or question..."
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                />
                <TouchableOpacity
                  style={styles.sendButton}
                  onPress={handleSendMessage}
                >
                  <Send size={16} color="#FFFFFF" />
                  <Text style={styles.sendButtonText}>Send Message</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Support Hours */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Support Hours</Text>
              <View style={styles.supportHours}>
                <Text style={styles.supportHoursText}>
                  Monday - Friday: 9:00 AM - 6:00 PM{'\n'}
                  Saturday: 10:00 AM - 4:00 PM{'\n'}
                  Sunday: Closed
                </Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 12,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  activeTabText: {
    color: '#EF4444',
  },
  content: {
    flex: 1,
  },
  faqContainer: {
    padding: 16,
  },
  contactContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  faqItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 8,
    overflow: 'hidden',
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  faqAnswer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  faqAnswerText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  contactSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  messageContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 16,
  },
  messageInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    height: 120,
    marginBottom: 12,
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
  },
  sendButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 6,
  },
  supportHours: {
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 8,
  },
  supportHoursText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
  },
});
