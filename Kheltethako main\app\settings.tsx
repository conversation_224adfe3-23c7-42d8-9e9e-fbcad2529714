import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  ArrowLeft,
  Bell,
  Shield,
  Globe,
  Moon,
  Volume2,
  Smartphone,
  ChevronRight,
  Trash2,
  LogOut,
} from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';

interface SettingItemProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightComponent?: React.ReactNode;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  rightComponent,
}) => (
  <TouchableOpacity style={styles.settingItem} onPress={onPress}>
    <View style={styles.settingLeft}>
      <View style={styles.settingIcon}>{icon}</View>
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingRight}>
      {rightComponent}
      {showArrow && !rightComponent && (
        <ChevronRight size={16} color="#9CA3AF" />
      )}
    </View>
  </TouchableOpacity>
);

export default function SettingsScreen() {
  const { signOut } = useAuth();
  const [settings, setSettings] = useState({
    pushNotifications: true,
    emailNotifications: true,
    smsNotifications: false,
    darkMode: false,
    soundEffects: true,
    vibration: true,
  });

  const toggleSetting = (key: string) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Account Deleted', 'Your account has been deleted successfully.');
          },
        },
      ]
    );
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          onPress: async () => {
            await signOut();
            router.replace('/(auth)/login');
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          <SettingItem
            icon={<Bell size={20} color="#EF4444" />}
            title="Push Notifications"
            subtitle="Receive booking updates and reminders"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.pushNotifications}
                onValueChange={() => toggleSetting('pushNotifications')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.pushNotifications ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
          <SettingItem
            icon={<Bell size={20} color="#EF4444" />}
            title="Email Notifications"
            subtitle="Get booking confirmations via email"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.emailNotifications}
                onValueChange={() => toggleSetting('emailNotifications')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.emailNotifications ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
          <SettingItem
            icon={<Smartphone size={20} color="#EF4444" />}
            title="SMS Notifications"
            subtitle="Receive SMS for important updates"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.smsNotifications}
                onValueChange={() => toggleSetting('smsNotifications')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.smsNotifications ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
        </View>

        {/* Appearance Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          <SettingItem
            icon={<Moon size={20} color="#EF4444" />}
            title="Dark Mode"
            subtitle="Switch to dark theme"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.darkMode}
                onValueChange={() => toggleSetting('darkMode')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.darkMode ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
          <SettingItem
            icon={<Globe size={20} color="#EF4444" />}
            title="Language"
            subtitle="English"
            onPress={() => console.log('Language settings')}
          />
        </View>

        {/* Sound & Haptics Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sound & Haptics</Text>
          <SettingItem
            icon={<Volume2 size={20} color="#EF4444" />}
            title="Sound Effects"
            subtitle="Play sounds for app interactions"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.soundEffects}
                onValueChange={() => toggleSetting('soundEffects')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.soundEffects ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
          <SettingItem
            icon={<Smartphone size={20} color="#EF4444" />}
            title="Vibration"
            subtitle="Vibrate for notifications and feedback"
            showArrow={false}
            rightComponent={
              <Switch
                value={settings.vibration}
                onValueChange={() => toggleSetting('vibration')}
                trackColor={{ false: '#E5E7EB', true: '#FCA5A5' }}
                thumbColor={settings.vibration ? '#EF4444' : '#9CA3AF'}
              />
            }
          />
        </View>

        {/* Privacy & Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>
          <SettingItem
            icon={<Shield size={20} color="#EF4444" />}
            title="Privacy Policy"
            onPress={() => console.log('Privacy Policy')}
          />
          <SettingItem
            icon={<Shield size={20} color="#EF4444" />}
            title="Terms of Service"
            onPress={() => console.log('Terms of Service')}
          />
        </View>

        {/* Account Actions Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <SettingItem
            icon={<LogOut size={20} color="#F59E0B" />}
            title="Sign Out"
            onPress={handleSignOut}
            showArrow={false}
          />
          <SettingItem
            icon={<Trash2 size={20} color="#EF4444" />}
            title="Delete Account"
            subtitle="Permanently delete your account and data"
            onPress={handleDeleteAccount}
            showArrow={false}
          />
        </View>

        <View style={styles.footer}>
          <Text style={styles.version}>KhelteThako v1.0.0</Text>
          <Text style={styles.copyright}>© 2025 KhelteThako. All rights reserved.</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 12,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingTop: 24,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FEF2F2',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  version: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 4,
  },
  copyright: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
});
