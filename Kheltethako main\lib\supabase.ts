import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://iymmfpbcawwzxloaovmm.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTgyNzUsImV4cCI6MjA2NDE3NDI3NX0.nwhd7PPfGz9m5MCyah0RcLc_7XXnpEg47uPRjLyKi2Y';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          phone: string | null;
          location: string | null;
          avatar_url: string | null;
          member_since: string | null;
          is_active: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          email: string;
          full_name: string;
          phone?: string | null;
          location?: string | null;
          avatar_url?: string | null;
          member_since?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string;
          phone?: string | null;
          location?: string | null;
          avatar_url?: string | null;
          member_since?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      sports: {
        Row: {
          id: number;
          name: string;
          icon: string | null;
          description: string | null;
          is_active: boolean | null;
          created_at: string | null;
        };
        Insert: {
          id?: number;
          name: string;
          icon?: string | null;
          description?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
        };
        Update: {
          id?: number;
          name?: string;
          icon?: string | null;
          description?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
        };
      };
      venue_categories: {
        Row: {
          id: number;
          name: string;
          description: string | null;
          is_active: boolean | null;
          created_at: string | null;
        };
        Insert: {
          id?: number;
          name: string;
          description?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
        };
        Update: {
          id?: number;
          name?: string;
          description?: string | null;
          is_active?: boolean | null;
          created_at?: string | null;
        };
      };
      venues: {
        Row: {
          id: number;
          name: string;
          slug: string;
          description: string | null;
          category_id: number | null;
          sport_id: number | null;
          address: string;
          city: string;
          area: string | null;
          latitude: number | null;
          longitude: number | null;
          price_per_hour: number;
          currency: string | null;
          capacity: number | null;
          surface_type: string | null;
          is_indoor: boolean | null;
          opening_time: string | null;
          closing_time: string | null;
          amenities: any | null;
          contact_phone: string | null;
          contact_email: string | null;
          manager_name: string | null;
          images: any | null;
          average_rating: number | null;
          total_reviews: number | null;
          is_active: boolean | null;
          is_verified: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          name: string;
          slug: string;
          description?: string | null;
          category_id?: number | null;
          sport_id?: number | null;
          address: string;
          city: string;
          area?: string | null;
          latitude?: number | null;
          longitude?: number | null;
          price_per_hour: number;
          currency?: string | null;
          capacity?: number | null;
          surface_type?: string | null;
          is_indoor?: boolean | null;
          opening_time?: string | null;
          closing_time?: string | null;
          amenities?: any | null;
          contact_phone?: string | null;
          contact_email?: string | null;
          manager_name?: string | null;
          images?: any | null;
          average_rating?: number | null;
          total_reviews?: number | null;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          name?: string;
          slug?: string;
          description?: string | null;
          category_id?: number | null;
          sport_id?: number | null;
          address?: string;
          city?: string;
          area?: string | null;
          latitude?: number | null;
          longitude?: number | null;
          price_per_hour?: number;
          currency?: string | null;
          capacity?: number | null;
          surface_type?: string | null;
          is_indoor?: boolean | null;
          opening_time?: string | null;
          closing_time?: string | null;
          amenities?: any | null;
          contact_phone?: string | null;
          contact_email?: string | null;
          manager_name?: string | null;
          images?: any | null;
          average_rating?: number | null;
          total_reviews?: number | null;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      coaches: {
        Row: {
          id: number;
          user_id: string | null;
          bio: string | null;
          experience_years: number | null;
          sport_id: number | null;
          specialties: any | null;
          certifications: any | null;
          hourly_rate: number;
          currency: string | null;
          available_days: any | null;
          available_times: any | null;
          city: string | null;
          area: string | null;
          travel_radius: number | null;
          languages: any | null;
          average_rating: number | null;
          total_reviews: number | null;
          avg_response_time: number | null;
          is_active: boolean | null;
          is_verified: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          user_id?: string | null;
          bio?: string | null;
          experience_years?: number | null;
          sport_id?: number | null;
          specialties?: any | null;
          certifications?: any | null;
          hourly_rate: number;
          currency?: string | null;
          available_days?: any | null;
          available_times?: any | null;
          city?: string | null;
          area?: string | null;
          travel_radius?: number | null;
          languages?: any | null;
          average_rating?: number | null;
          total_reviews?: number | null;
          avg_response_time?: number | null;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          user_id?: string | null;
          bio?: string | null;
          experience_years?: number | null;
          sport_id?: number | null;
          specialties?: any | null;
          certifications?: any | null;
          hourly_rate?: number;
          currency?: string | null;
          available_days?: any | null;
          available_times?: any | null;
          city?: string | null;
          area?: string | null;
          travel_radius?: number | null;
          languages?: any | null;
          average_rating?: number | null;
          total_reviews?: number | null;
          avg_response_time?: number | null;
          is_active?: boolean | null;
          is_verified?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      bookings: {
        Row: {
          id: number;
          booking_number: string;
          user_id: string | null;
          venue_id: number | null;
          booking_date: string;
          start_time: string;
          end_time: string;
          duration_hours: number;
          total_players: number | null;
          special_requests: string | null;
          price_per_hour: number;
          total_amount: number;
          currency: string | null;
          status: string | null;
          payment_status: string | null;
          payment_method: string | null;
          payment_transaction_id: string | null;
          paid_at: string | null;
          cancelled_at: string | null;
          cancellation_reason: string | null;
          refund_amount: number | null;
          refunded_at: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          booking_number: string;
          user_id?: string | null;
          venue_id?: number | null;
          booking_date: string;
          start_time: string;
          end_time: string;
          duration_hours: number;
          total_players?: number | null;
          special_requests?: string | null;
          price_per_hour: number;
          total_amount: number;
          currency?: string | null;
          status?: string | null;
          payment_status?: string | null;
          payment_method?: string | null;
          payment_transaction_id?: string | null;
          paid_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          refund_amount?: number | null;
          refunded_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          booking_number?: string;
          user_id?: string | null;
          venue_id?: number | null;
          booking_date?: string;
          start_time?: string;
          end_time?: string;
          duration_hours?: number;
          total_players?: number | null;
          special_requests?: string | null;
          price_per_hour?: number;
          total_amount?: number;
          currency?: string | null;
          status?: string | null;
          payment_status?: string | null;
          payment_method?: string | null;
          payment_transaction_id?: string | null;
          paid_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          refund_amount?: number | null;
          refunded_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      reviews: {
        Row: {
          id: number;
          user_id: string | null;
          venue_id: number | null;
          coach_id: number | null;
          booking_id: number | null;
          rating: number;
          title: string | null;
          comment: string;
          photos: any | null;
          is_approved: boolean | null;
          is_featured: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          user_id?: string | null;
          venue_id?: number | null;
          coach_id?: number | null;
          booking_id?: number | null;
          rating: number;
          title?: string | null;
          comment: string;
          photos?: any | null;
          is_approved?: boolean | null;
          is_featured?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          user_id?: string | null;
          venue_id?: number | null;
          coach_id?: number | null;
          booking_id?: number | null;
          rating?: number;
          title?: string | null;
          comment?: string;
          photos?: any | null;
          is_approved?: boolean | null;
          is_featured?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      favorites: {
        Row: {
          id: number;
          user_id: string | null;
          venue_id: number | null;
          coach_id: number | null;
          created_at: string | null;
        };
        Insert: {
          id?: number;
          user_id?: string | null;
          venue_id?: number | null;
          coach_id?: number | null;
          created_at?: string | null;
        };
        Update: {
          id?: number;
          user_id?: string | null;
          venue_id?: number | null;
          coach_id?: number | null;
          created_at?: string | null;
        };
      };
      notifications: {
        Row: {
          id: number;
          user_id: string | null;
          title: string;
          message: string;
          type: string | null;
          is_read: boolean | null;
          action_url: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: number;
          user_id?: string | null;
          title: string;
          message: string;
          type?: string | null;
          is_read?: boolean | null;
          action_url?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: number;
          user_id?: string | null;
          title?: string;
          message?: string;
          type?: string | null;
          is_read?: boolean | null;
          action_url?: string | null;
          created_at?: string | null;
        };
      };
      coach_sessions: {
        Row: {
          id: number;
          session_number: string;
          user_id: string | null;
          coach_id: number | null;
          session_date: string;
          start_time: string;
          end_time: string;
          duration_hours: number;
          session_type: string | null;
          player_count: number | null;
          skill_level: string | null;
          session_goals: string | null;
          special_requests: string | null;
          price_per_hour: number;
          total_amount: number;
          currency: string | null;
          status: string | null;
          payment_status: string | null;
          payment_method: string | null;
          payment_transaction_id: string | null;
          paid_at: string | null;
          cancelled_at: string | null;
          cancellation_reason: string | null;
          refund_amount: number | null;
          refunded_at: string | null;
          meeting_location: string | null;
          meeting_type: string | null;
          meeting_link: string | null;
          coach_notes: string | null;
          student_notes: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: number;
          session_number: string;
          user_id?: string | null;
          coach_id?: number | null;
          session_date: string;
          start_time: string;
          end_time: string;
          duration_hours: number;
          session_type?: string | null;
          player_count?: number | null;
          skill_level?: string | null;
          session_goals?: string | null;
          special_requests?: string | null;
          price_per_hour: number;
          total_amount: number;
          currency?: string | null;
          status?: string | null;
          payment_status?: string | null;
          payment_method?: string | null;
          payment_transaction_id?: string | null;
          paid_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          refund_amount?: number | null;
          refunded_at?: string | null;
          meeting_location?: string | null;
          meeting_type?: string | null;
          meeting_link?: string | null;
          coach_notes?: string | null;
          student_notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: number;
          session_number?: string;
          user_id?: string | null;
          coach_id?: number | null;
          session_date?: string;
          start_time?: string;
          end_time?: string;
          duration_hours?: number;
          session_type?: string | null;
          player_count?: number | null;
          skill_level?: string | null;
          session_goals?: string | null;
          special_requests?: string | null;
          price_per_hour?: number;
          total_amount?: number;
          currency?: string | null;
          status?: string | null;
          payment_status?: string | null;
          payment_method?: string | null;
          payment_transaction_id?: string | null;
          paid_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          refund_amount?: number | null;
          refunded_at?: string | null;
          meeting_location?: string | null;
          meeting_type?: string | null;
          meeting_link?: string | null;
          coach_notes?: string | null;
          student_notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
