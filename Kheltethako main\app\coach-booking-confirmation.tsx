import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Calendar, Clock, MapPin, CreditCard, User, Star } from 'lucide-react-native';

export default function CoachBookingConfirmationScreen() {
  const params = useLocalSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // Parse the time slot data
  const timeSlot = params.timeSlot ? JSON.parse(params.timeSlot as string) : null;
  const sessionFee = timeSlot?.price || parseInt(params.price as string) || 1500;
  const serviceFee = Math.round(sessionFee * 0.05); // 5% service fee
  const totalAmount = sessionFee + serviceFee;

  const handleConfirmBooking = async () => {
    try {
      setIsLoading(true);
      // Navigate to payment with coach booking data
      router.push({
        pathname: '/payment',
        params: {
          coachId: params.coachId,
          coachName: params.coachName,
          date: params.date,
          timeSlot: params.timeSlot,
          totalAmount: totalAmount.toString(),
          type: 'coach',
        },
      });
    } catch (error) {
      console.error('Error confirming coach booking:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Confirm Coach Session</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Coach Details</Text>
          <View style={styles.coachContainer}>
            <View style={styles.coachInfo}>
              <Image
                source={{ uri: params.coachImage as string || 'https://images.pexels.com/photos/1043474/pexels-photo-1043474.jpeg' }}
                style={styles.coachImage}
              />
              <View style={styles.coachDetails}>
                <Text style={styles.coachName}>{params.coachName || 'Coach'}</Text>
                <Text style={styles.coachSpecialty}>{params.specialty || 'Professional Coach'}</Text>
                <View style={styles.ratingContainer}>
                  <Star size={16} color="#F59E0B" fill="#F59E0B" />
                  <Text style={styles.rating}>{params.rating || '4.8'}</Text>
                  <Text style={styles.reviewCount}>({params.reviewCount || '156'} reviews)</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Session Details</Text>
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <Calendar size={18} color="#6B7280" />
              <Text style={styles.detailText}>
                {new Date(params.date as string).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Clock size={18} color="#6B7280" />
              <Text style={styles.detailText}>
                {timeSlot ? `${timeSlot.start_time} - ${timeSlot.end_time}` : 'Time not selected'}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <MapPin size={18} color="#6B7280" />
              <Text style={styles.detailText}>{params.location || 'Location TBD'}</Text>
            </View>
            <View style={styles.detailRow}>
              <User size={18} color="#6B7280" />
              <Text style={styles.detailText}>1-on-1 Personal Training</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Price Breakdown</Text>
          <View style={styles.priceContainer}>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Session Fee (1 hour)</Text>
              <Text style={styles.priceValue}>৳{sessionFee}</Text>
            </View>
            <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Service Fee (5%)</Text>
              <Text style={styles.priceValue}>৳{serviceFee}</Text>
            </View>
            <View style={styles.divider} />
            <View style={styles.priceRow}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>৳{totalAmount}</Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          <TouchableOpacity style={styles.paymentMethod}>
            <CreditCard size={20} color="#111827" />
            <Text style={styles.paymentMethodText}>Add Payment Method</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cancellation Policy</Text>
          <Text style={styles.policyText}>
            Free cancellation up to 2 hours before the session. After that, you'll be charged 50% of the session fee.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>What to Expect</Text>
          <Text style={styles.expectationText}>
            • Personalized training session tailored to your goals{'\n'}
            • Professional coaching and technique guidance{'\n'}
            • Equipment provided (if needed){'\n'}
            • Progress tracking and feedback
          </Text>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.confirmButton, isLoading && styles.confirmButtonDisabled]}
          onPress={handleConfirmBooking}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" />
          ) : (
            <Text style={styles.confirmButtonText}>Confirm & Pay</Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 12,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  coachContainer: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  coachInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coachImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  coachDetails: {
    flex: 1,
  },
  coachName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  coachSpecialty: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 4,
  },
  reviewCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  detailsContainer: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    marginLeft: 8,
  },
  priceContainer: {
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  priceLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  priceValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 8,
  },
  totalLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  totalValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#EF4444',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  paymentMethodText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
    marginLeft: 8,
  },
  policyText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  expectationText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  confirmButton: {
    backgroundColor: '#EF4444',
    height: 44,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmButtonDisabled: {
    opacity: 0.7,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
});
