import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { 
  ArrowLeft, 
  TrendingUp, 
  DollarSign, 
  Users, 
  CreditCard,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react-native';
import {
  getPaymentAnalyticsSummary,
  getRealtimeMetrics,
  getPaymentMethodBreakdown,
  getDailyRevenueTrends,
  getTopVenuesByRevenue,
  getUserEngagementMetrics,
  exportAnalyticsData,
  type PaymentAnalyticsSummary,
  type RealtimeMetrics,
  type PaymentMethodBreakdown,
  type DailyRevenueTrend,
} from '@/services/analyticsApi';

const { width } = Dimensions.get('window');

export default function AnalyticsScreen() {
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d'>('30d');
  
  // Analytics data state
  const [summary, setSummary] = useState<PaymentAnalyticsSummary | null>(null);
  const [realtimeMetrics, setRealtimeMetrics] = useState<RealtimeMetrics | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodBreakdown[]>([]);
  const [revenueTrends, setRevenueTrends] = useState<DailyRevenueTrend[]>([]);
  const [topVenues, setTopVenues] = useState<any[]>([]);
  const [userMetrics, setUserMetrics] = useState<any>(null);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(
        Date.now() - (selectedPeriod === '7d' ? 7 : selectedPeriod === '30d' ? 30 : 90) * 24 * 60 * 60 * 1000
      ).toISOString().split('T')[0];

      // Load all analytics data in parallel
      const [
        summaryData,
        realtimeData,
        paymentMethodsData,
        revenueTrendsData,
        topVenuesData,
        userMetricsData,
      ] = await Promise.all([
        getPaymentAnalyticsSummary(startDate, endDate),
        getRealtimeMetrics(),
        getPaymentMethodBreakdown(startDate, endDate),
        getDailyRevenueTrends(startDate, endDate),
        getTopVenuesByRevenue(startDate, endDate, 5),
        getUserEngagementMetrics(startDate, endDate),
      ]);

      setSummary(summaryData);
      setRealtimeMetrics(realtimeData);
      setPaymentMethods(paymentMethodsData);
      setRevenueTrends(revenueTrendsData);
      setTopVenues(topVenuesData);
      setUserMetrics(userMetricsData);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const handleExportData = async (type: 'payments' | 'bookings' | 'users') => {
    try {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(
        Date.now() - (selectedPeriod === '7d' ? 7 : selectedPeriod === '30d' ? 30 : 90) * 24 * 60 * 60 * 1000
      ).toISOString().split('T')[0];

      const csvData = await exportAnalyticsData(type, startDate, endDate);
      
      // In a real app, you would save this to device storage or share it
      console.log('CSV Data:', csvData);
      // You could use expo-sharing or expo-file-system to save/share the file
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const formatCurrency = (amount: number) => {
    return `৳${amount.toLocaleString()}`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const MetricCard = ({ title, value, subtitle, icon: Icon, color = '#0B8457' }: any) => (
    <View style={styles.metricCard}>
      <View style={styles.metricHeader}>
        <Icon size={20} color={color} />
        <Text style={styles.metricTitle}>{title}</Text>
      </View>
      <Text style={[styles.metricValue, { color }]}>{value}</Text>
      {subtitle && <Text style={styles.metricSubtitle}>{subtitle}</Text>}
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Analytics Dashboard</Text>
        <TouchableOpacity onPress={onRefresh} style={styles.refreshButton}>
          <RefreshCw size={20} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Period Selector */}
      <View style={styles.periodSelector}>
        {(['7d', '30d', '90d'] as const).map((period) => (
          <TouchableOpacity
            key={period}
            style={[
              styles.periodButton,
              selectedPeriod === period && styles.periodButtonActive,
            ]}
            onPress={() => setSelectedPeriod(period)}
          >
            <Text
              style={[
                styles.periodButtonText,
                selectedPeriod === period && styles.periodButtonTextActive,
              ]}
            >
              {period === '7d' ? '7 Days' : period === '30d' ? '30 Days' : '90 Days'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Real-time Metrics */}
        {realtimeMetrics && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Today's Overview</Text>
            <View style={styles.metricsGrid}>
              <MetricCard
                title="Revenue"
                value={formatCurrency(realtimeMetrics.today_revenue)}
                subtitle="Today"
                icon={DollarSign}
                color="#0B8457"
              />
              <MetricCard
                title="Transactions"
                value={realtimeMetrics.today_transactions.toString()}
                subtitle="Today"
                icon={CreditCard}
                color="#3B82F6"
              />
              <MetricCard
                title="Active Bookings"
                value={realtimeMetrics.active_bookings.toString()}
                subtitle="Current"
                icon={Calendar}
                color="#F59E0B"
              />
              <MetricCard
                title="Success Rate"
                value={formatPercentage(realtimeMetrics.success_rate_today)}
                subtitle="Today"
                icon={TrendingUp}
                color="#10B981"
              />
            </View>
          </View>
        )}

        {/* Summary Metrics */}
        {summary && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              {selectedPeriod === '7d' ? '7-Day' : selectedPeriod === '30d' ? '30-Day' : '90-Day'} Summary
            </Text>
            <View style={styles.summaryCard}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Revenue</Text>
                <Text style={styles.summaryValue}>{formatCurrency(summary.total_revenue)}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Net Revenue</Text>
                <Text style={styles.summaryValue}>{formatCurrency(summary.net_revenue)}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Transactions</Text>
                <Text style={styles.summaryValue}>{summary.total_transactions}</Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Success Rate</Text>
                <Text style={[styles.summaryValue, { color: '#10B981' }]}>
                  {formatPercentage(summary.success_rate)}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Avg. Transaction</Text>
                <Text style={styles.summaryValue}>{formatCurrency(summary.avg_transaction_value)}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Payment Methods */}
        {paymentMethods.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Payment Methods</Text>
            <View style={styles.paymentMethodsCard}>
              {paymentMethods.map((method, index) => (
                <View key={index} style={styles.paymentMethodRow}>
                  <Text style={styles.paymentMethodName}>{method.payment_method}</Text>
                  <View style={styles.paymentMethodStats}>
                    <Text style={styles.paymentMethodCount}>{method.transactions}</Text>
                    <Text style={styles.paymentMethodPercentage}>
                      {formatPercentage(method.percentage)}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Top Venues */}
        {topVenues.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Top Performing Venues</Text>
            <View style={styles.topVenuesCard}>
              {topVenues.map((venue, index) => (
                <View key={index} style={styles.venueRow}>
                  <View style={styles.venueInfo}>
                    <Text style={styles.venueName}>{venue.venue_name}</Text>
                    <Text style={styles.venueBookings}>{venue.booking_count} bookings</Text>
                  </View>
                  <Text style={styles.venueRevenue}>{formatCurrency(venue.total_revenue)}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* User Engagement */}
        {userMetrics && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Engagement</Text>
            <View style={styles.userMetricsCard}>
              <View style={styles.userMetricRow}>
                <Text style={styles.userMetricLabel}>New Users</Text>
                <Text style={styles.userMetricValue}>{userMetrics.new_users}</Text>
              </View>
              <View style={styles.userMetricRow}>
                <Text style={styles.userMetricLabel}>Active Users</Text>
                <Text style={styles.userMetricValue}>{userMetrics.active_users}</Text>
              </View>
              <View style={styles.userMetricRow}>
                <Text style={styles.userMetricLabel}>Repeat Customers</Text>
                <Text style={styles.userMetricValue}>{userMetrics.repeat_customers}</Text>
              </View>
              <View style={styles.userMetricRow}>
                <Text style={styles.userMetricLabel}>Retention Rate</Text>
                <Text style={[styles.userMetricValue, { color: '#10B981' }]}>
                  {formatPercentage(userMetrics.retention_rate)}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Export Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Export Data</Text>
          <View style={styles.exportButtons}>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => handleExportData('payments')}
            >
              <Download size={16} color="#0B8457" />
              <Text style={styles.exportButtonText}>Payments</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => handleExportData('bookings')}
            >
              <Download size={16} color="#0B8457" />
              <Text style={styles.exportButtonText}>Bookings</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.exportButton}
              onPress={() => handleExportData('users')}
            >
              <Download size={16} color="#0B8457" />
              <Text style={styles.exportButtonText}>Users</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  refreshButton: {
    padding: 4,
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#0B8457',
    borderColor: '#0B8457',
  },
  periodButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  periodButtonTextActive: {
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  metricCard: {
    width: (width - 52) / 2,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricTitle: {
    marginLeft: 8,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  metricValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    marginBottom: 4,
  },
  metricSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  summaryCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  summaryValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  paymentMethodsCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  paymentMethodRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  paymentMethodName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  paymentMethodStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  paymentMethodCount: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  paymentMethodPercentage: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
  },
  topVenuesCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  venueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  venueInfo: {
    flex: 1,
  },
  venueName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  venueBookings: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  venueRevenue: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#0B8457',
  },
  userMetricsCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  userMetricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  userMetricLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  userMetricValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  exportButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  exportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0B8457',
    backgroundColor: '#ECFDF5',
  },
  exportButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
  },
});
