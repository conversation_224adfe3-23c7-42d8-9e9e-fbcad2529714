import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { XCircle, Home, RotateCcw, CreditCard } from 'lucide-react-native';
import { updateBookingStatus } from '@/services/supabaseApi';

export default function PaymentCancelScreen() {
  const params = useLocalSearchParams();

  useEffect(() => {
    // Update booking status to cancelled if payment was cancelled
    if (params.booking_id) {
      updateBookingStatus(params.booking_id as string, 'cancelled', {
        cancellation_reason: 'Payment cancelled by user',
      }).catch(error => {
        console.error('Error updating booking status:', error);
      });
    }
  }, [params.booking_id]);

  const handleRetryPayment = () => {
    // Navigate back to payment screen with the same booking data
    router.back();
  };

  const handleGoHome = () => {
    router.push('/(tabs)/home');
  };

  const handleTryAgain = () => {
    // Navigate back to venue/coach selection
    router.push('/(tabs)/explore');
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.centerContainer}>
        <View style={styles.iconContainer}>
          <View style={styles.iconCircle}>
            <XCircle size={48} color="#EF4444" />
          </View>
        </View>

        <Text style={styles.title}>Payment Cancelled</Text>
        <Text style={styles.subtitle}>
          Your payment was cancelled and no money has been charged. Your booking has been cancelled.
        </Text>

        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>What happened?</Text>
          <Text style={styles.detailsText}>
            • You cancelled the payment process{'\n'}
            • No money was deducted from your account{'\n'}
            • Your booking has been automatically cancelled{'\n'}
            • You can try booking again anytime
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleRetryPayment}>
            <CreditCard size={20} color="#FFFFFF" />
            <Text style={styles.primaryButtonText}>Retry Payment</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={handleTryAgain}>
            <RotateCcw size={20} color="#0B8457" />
            <Text style={styles.secondaryButtonText}>Book Again</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.textButton} onPress={handleGoHome}>
            <Home size={16} color="#6B7280" />
            <Text style={styles.textButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.helpContainer}>
          <Text style={styles.helpTitle}>Need Help?</Text>
          <Text style={styles.helpText}>
            If you're having trouble with payments, please contact our support team.
          </Text>
          <TouchableOpacity style={styles.helpButton}>
            <Text style={styles.helpButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    marginBottom: 24,
  },
  iconCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FEE2E2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  detailsContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    marginBottom: 32,
  },
  detailsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  detailsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
    marginBottom: 32,
  },
  primaryButton: {
    flexDirection: 'row',
    backgroundColor: '#0B8457',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    marginLeft: 8,
  },
  secondaryButton: {
    flexDirection: 'row',
    backgroundColor: '#ECFDF5',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1FAE5',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
    marginLeft: 8,
  },
  textButton: {
    flexDirection: 'row',
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 4,
  },
  helpContainer: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    padding: 16,
    width: '100%',
    alignItems: 'center',
  },
  helpTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  helpText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 12,
  },
  helpButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  helpButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
  },
});
