import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MapPin, Calendar, Clock, BadgeCheck } from 'lucide-react-native';
import { getUserBookings } from '@/services/supabaseApi';
import { useAuth } from '@/contexts/AuthContext';

export default function BookingsScreen() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('upcoming');
  const [bookings, setBookings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  useEffect(() => {
    fetchBookings();
  }, [activeTab]);

  const fetchBookings = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const data = await getUserBookings(user.id, activeTab);
      setBookings(data);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { weekday: 'short', day: 'numeric', month: 'short', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };

  const formatTime = (timeString) => {
    return timeString;
  };

  // Show login prompt for unauthenticated users
  if (!user) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <Text style={styles.title}>My Bookings</Text>
        </View>

        <View style={styles.loginPromptContainer}>
          <View style={styles.loginPromptContent}>
            <Calendar size={64} color="#0B8457" style={styles.loginIcon} />
            <Text style={styles.loginPromptTitle}>Sign in to view bookings</Text>
            <Text style={styles.loginPromptSubtitle}>
              Track your venue and coach bookings, view history, and manage upcoming sessions
            </Text>

            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>Sign In</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.registerButton} onPress={handleRegister}>
              <Text style={styles.registerButtonText}>Create Account</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  const renderBookingCard = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.bookingCard}
        onPress={() => router.push(`/booking-details/${item.id}`)}
      >
        <View style={styles.bookingHeader}>
          <Text style={styles.bookingType}>
            {item.type === 'venue' ? 'Venue Booking' : 'Coach Session'}
          </Text>
          <View
            style={[
              styles.statusBadge,
              item.status === 'confirmed'
                ? styles.confirmedBadge
                : item.status === 'pending'
                ? styles.pendingBadge
                : styles.cancelledBadge,
            ]}
          >
            <Text
              style={[
                styles.statusText,
                item.status === 'confirmed'
                  ? styles.confirmedText
                  : item.status === 'pending'
                  ? styles.pendingText
                  : styles.cancelledText,
              ]}
            >
              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
            </Text>
          </View>
        </View>

        <View style={styles.bookingContent}>
          <Image source={{ uri: item.image_url }} style={styles.bookingImage} />
          <View style={styles.bookingDetails}>
            <Text style={styles.bookingName}>{item.name}</Text>
            <View style={styles.bookingLocation}>
              <MapPin size={14} color="#6B7280" />
              <Text style={styles.bookingLocationText}>{item.location}</Text>
            </View>
            <View style={styles.bookingTimeContainer}>
              <View style={styles.bookingTime}>
                <Calendar size={14} color="#6B7280" />
                <Text style={styles.bookingTimeText}>
                  {formatDate(item.date)}
                </Text>
              </View>
              <View style={styles.bookingTime}>
                <Clock size={14} color="#6B7280" />
                <Text style={styles.bookingTimeText}>
                  {formatTime(item.start_time)} - {formatTime(item.end_time)}
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.bookingFooter}>
          <Text style={styles.bookingPrice}>
            ৳{item.total_price.toFixed(2)}
          </Text>
          {item.status === 'confirmed' && (
            <View style={styles.confirmedTag}>
              <BadgeCheck size={14} color="#0B8457" />
              <Text style={styles.confirmedTagText}>Confirmed</Text>
            </View>
          )}
        </View>

        {activeTab === 'upcoming' && item.status === 'confirmed' && (
          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel Booking</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <Text style={styles.title}>My Bookings</Text>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'upcoming' && styles.activeTab]}
          onPress={() => setActiveTab('upcoming')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'upcoming' && styles.activeTabText,
            ]}
          >
            Upcoming
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'past' && styles.activeTab]}
          onPress={() => setActiveTab('past')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'past' && styles.activeTabText,
            ]}
          >
            Past
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'cancelled' && styles.activeTab]}
          onPress={() => setActiveTab('cancelled')}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === 'cancelled' && styles.activeTabText,
            ]}
          >
            Cancelled
          </Text>
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0B8457" />
        </View>
      ) : (
        <FlatList
          data={bookings}
          renderItem={renderBookingCard}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.bookingsList}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateTitle}>
                No {activeTab} bookings
              </Text>
              <Text style={styles.emptyStateText}>
                {activeTab === 'upcoming'
                  ? "You don't have any upcoming bookings. Start exploring venues and coaches to book your next session!"
                  : activeTab === 'past'
                  ? "You don't have any past bookings."
                  : "You don't have any cancelled bookings."}
              </Text>
              {activeTab === 'upcoming' && (
                <TouchableOpacity
                  style={styles.exploreButton}
                  onPress={() => router.push('/(tabs)/explore')}
                >
                  <Text style={styles.exploreButtonText}>
                    Explore Venues & Coaches
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  activeTab: {
    backgroundColor: '#0B8457',
  },
  tabText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#6B7280',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bookingsList: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  bookingCard: {
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    padding: 16,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  bookingType: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    color: '#6B7280',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  confirmedBadge: {
    backgroundColor: '#ECFDF5',
  },
  pendingBadge: {
    backgroundColor: '#FEF3C7',
  },
  cancelledBadge: {
    backgroundColor: '#FEE2E2',
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  confirmedText: {
    color: '#059669',
  },
  pendingText: {
    color: '#D97706',
  },
  cancelledText: {
    color: '#DC2626',
  },
  bookingContent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  bookingImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  bookingDetails: {
    flex: 1,
    marginLeft: 12,
  },
  bookingName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  bookingLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  bookingLocationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  bookingTimeContainer: {
    flexDirection: 'column',
    gap: 4,
  },
  bookingTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookingTimeText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  bookingPrice: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#0B8457',
  },
  confirmedTag: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  confirmedTagText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
    marginLeft: 4,
  },
  cancelButton: {
    marginTop: 12,
    paddingVertical: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#EF4444',
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  emptyState: {
    paddingVertical: 40,
    alignItems: 'center',
  },
  emptyStateTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    color: '#374151',
    marginBottom: 8,
  },
  emptyStateText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  exploreButton: {
    backgroundColor: '#0B8457',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  exploreButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  loginPromptContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  loginPromptContent: {
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 32,
  },
  loginIcon: {
    marginBottom: 16,
  },
  loginPromptTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 8,
  },
  loginPromptSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  loginButton: {
    backgroundColor: '#0B8457',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    marginBottom: 12,
    width: '100%',
  },
  loginButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  registerButton: {
    backgroundColor: 'transparent',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0B8457',
    width: '100%',
  },
  registerButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
    textAlign: 'center',
  },
});