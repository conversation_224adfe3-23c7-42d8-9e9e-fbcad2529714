# Supabase Configuration for KhelteThako

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://kheltethako.com"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
security_update_password_require_reauthentication = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.google]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.facebook]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[db]
port = 54322

[studio]
enabled = true
port = 54323

[inbucket]
enabled = true
port = 54324

[storage]
enabled = true
port = 54325
file_size_limit = "50MiB"

[edge_runtime]
enabled = true
port = 54326

[analytics]
enabled = false

[functions.uddoktapay-webhook]
verify_jwt = false

[functions.push-notifications]
verify_jwt = true

# Environment variables for Edge Functions
[env]
SUPABASE_URL = "https://iymmfpbcawwzxloaovmm.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTgyNzUsImV4cCI6MjA2NDE3NDI3NX0.nwhd7PPfGz9m5MCyah0RcLc_7XXnpEg47uPRjLyKi2Y"
# Add your FCM Server Key for push notifications
FCM_SERVER_KEY = "your-fcm-server-key-here"
# Add your UddoktaPay webhook secret for signature validation
UDDOKTAPAY_WEBHOOK_SECRET = "your-webhook-secret-here"
