import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Heart, MapPin, Star, Users } from 'lucide-react-native';

// Mock data for favorites
const mockFavorites = {
  venues: [
    {
      id: 1,
      name: 'Dhaka Cricket Academy',
      location: 'Mirpur, Dhaka',
      rating: 4.8,
      reviews_count: 124,
      price_per_hour: 2500,
      image_url: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      type: 'venue',
    },
    {
      id: 2,
      name: 'Goal Football Arena',
      location: 'Banani, Dhaka',
      rating: 4.5,
      reviews_count: 89,
      price_per_hour: 3000,
      image_url: 'https://images.pexels.com/photos/274422/pexels-photo-274422.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      type: 'venue',
    },
  ],
  coaches: [
    {
      id: 1,
      name: 'Mashrafe Mortaza',
      specialty: 'Cricket Bowling',
      rating: 4.9,
      reviews_count: 87,
      price_per_session: 5000,
      image_url: 'https://images.pexels.com/photos/6551155/pexels-photo-6551155.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      type: 'coach',
    },
    {
      id: 2,
      name: 'Jamal Bhuyan',
      specialty: 'Football Midfielder',
      rating: 4.7,
      reviews_count: 54,
      price_per_session: 4500,
      image_url: 'https://images.pexels.com/photos/6638814/pexels-photo-6638814.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
      type: 'coach',
    },
  ],
};

export default function FavoritesScreen() {
  const [activeTab, setActiveTab] = useState('venues');
  const [favorites, setFavorites] = useState(mockFavorites);
  const [isLoading, setIsLoading] = useState(false);

  const handleRemoveFavorite = (id: number, type: string) => {
    setFavorites(prev => ({
      ...prev,
      [type]: prev[type].filter(item => item.id !== id),
    }));
  };

  const handleItemPress = (item: any) => {
    if (item.type === 'venue') {
      router.push(`/venue/${item.id}`);
    } else {
      router.push(`/coach/${item.id}`);
    }
  };

  const renderFavoriteItem = ({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.favoriteCard}
      onPress={() => handleItemPress(item)}
    >
      <Image source={{ uri: item.image_url }} style={styles.itemImage} />
      <View style={styles.itemDetails}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemName} numberOfLines={1}>
            {item.name}
          </Text>
          <TouchableOpacity
            style={styles.heartButton}
            onPress={() => handleRemoveFavorite(item.id, activeTab)}
          >
            <Heart size={18} color="#EF4444" fill="#EF4444" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.itemLocation}>
          <MapPin size={12} color="#6B7280" />
          <Text style={styles.itemLocationText} numberOfLines={1}>
            {item.location || item.specialty}
          </Text>
        </View>

        <View style={styles.itemFooter}>
          <View style={styles.itemRating}>
            <Star size={12} color="#F59E0B" fill="#F59E0B" />
            <Text style={styles.itemRatingText}>
              {item.rating} ({item.reviews_count})
            </Text>
          </View>
          <Text style={styles.itemPrice}>
            ৳{item.price_per_hour || item.price_per_session}
            {item.type === 'venue' ? '/hr' : '/session'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Heart size={48} color="#E5E7EB" />
      <Text style={styles.emptyStateTitle}>
        No {activeTab} in favorites
      </Text>
      <Text style={styles.emptyStateText}>
        Start exploring and add your favorite {activeTab} to see them here!
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => router.push('/(tabs)/explore')}
      >
        <Text style={styles.exploreButtonText}>Explore {activeTab}</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Favorites</Text>
      </View>

      {/* Tab Selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'venues' && styles.activeTab]}
          onPress={() => setActiveTab('venues')}
        >
          <Text style={[styles.tabText, activeTab === 'venues' && styles.activeTabText]}>
            Venues ({favorites.venues.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'coaches' && styles.activeTab]}
          onPress={() => setActiveTab('coaches')}
        >
          <Text style={[styles.tabText, activeTab === 'coaches' && styles.activeTabText]}>
            Coaches ({favorites.coaches.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#EF4444" />
          </View>
        ) : (
          <FlatList
            data={favorites[activeTab]}
            renderItem={renderFavoriteItem}
            keyExtractor={(item) => `${activeTab}-${item.id}`}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={renderEmptyState}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 12,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    margin: 16,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  activeTabText: {
    color: '#111827',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 16,
  },
  favoriteCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemImage: {
    width: 100,
    height: 100,
  },
  itemDetails: {
    flex: 1,
    padding: 12,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    marginRight: 8,
  },
  heartButton: {
    padding: 4,
  },
  itemLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemLocationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
    flex: 1,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#EF4444',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  exploreButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
  },
  exploreButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});
