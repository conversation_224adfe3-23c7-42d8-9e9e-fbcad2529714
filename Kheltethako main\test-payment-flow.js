// Test the complete payment flow
const { createClient } = require('@supabase/supabase-js');
const { default: fetch } = require('node-fetch');

const supabaseUrl = 'https://iymmfpbcawwzxloaovmm.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml5bW1mcGJjYXd3enhsb2Fvdm1tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTgyNzUsImV4cCI6MjA2NDE3NDI3NX0.nwhd7PPfGz9m5MCyah0RcLc_7XXnpEg47uPRjLyKi2Y';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// UddoktaPay configuration
const UDDOKTAPAY_API_KEY = 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU';
const UDDOKTAPAY_BASE_URL = 'https://digitaldot.paymently.io/api';

async function testCompletePaymentFlow() {
  console.log('🧪 Testing Complete Payment Flow...\n');

  try {
    // Step 1: Get a venue and available time slot
    console.log('1️⃣ Getting venue and time slot...');
    
    const { data: venues, error: venueError } = await supabase
      .from('venues')
      .select('*')
      .eq('is_active', true)
      .limit(1);

    if (venueError || !venues.length) {
      throw new Error('No venues found');
    }

    const venue = venues[0];
    console.log(`✅ Found venue: ${venue.name} (₹${venue.price_per_hour}/hr)`);

    // Get available time slots for today
    const today = new Date().toISOString().split('T')[0];
    const { data: timeSlots, error: slotsError } = await supabase
      .from('available_time_slots')
      .select('*')
      .eq('venue_id', venue.id)
      .eq('date', today)
      .eq('is_available', true)
      .limit(1);

    if (slotsError || !timeSlots.length) {
      throw new Error('No available time slots found');
    }

    const timeSlot = timeSlots[0];
    console.log(`✅ Found time slot: ${timeSlot.start_time} - ${timeSlot.end_time} (₹${timeSlot.price})`);

    // Step 2: Create a booking
    console.log('\n2️⃣ Creating booking...');
    
    const testUserId = '568efb69-7ba7-454a-b44b-ef25f65af83e'; // Existing test user
    
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert({
        user_id: testUserId,
        venue_id: venue.id,
        booking_date: today,
        start_time: timeSlot.start_time,
        end_time: timeSlot.end_time,
        duration_hours: 1,
        player_count: 10,
        price_per_hour: timeSlot.price,
        total_amount: timeSlot.price,
        currency: 'BDT',
        status: 'pending',
        payment_status: 'pending',
      })
      .select()
      .single();

    if (bookingError) {
      throw new Error('Failed to create booking: ' + bookingError.message);
    }

    console.log(`✅ Booking created: ID ${booking.id}`);

    // Step 3: Initialize payment with UddoktaPay
    console.log('\n3️⃣ Initializing payment...');
    
    const invoiceId = `KT_VENUE_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    
    const paymentData = {
      full_name: 'Test User',
      email: '<EMAIL>',
      amount: timeSlot.price,
      payment_type: 'no-emi',
      redirect_url: `http://localhost:8082/payment-success?booking_id=${booking.id}&invoice_id=${invoiceId}`,
      cancel_url: `http://localhost:8082/payment-cancel?booking_id=${booking.id}&invoice_id=${invoiceId}`,
      webhook_url: 'https://webhook.site/unique-id',
      metadata: {
        booking_id: booking.id.toString(),
        user_id: testUserId,
        booking_type: 'venue',
        app_name: 'KhelteThako'
      }
    };

    const response = await fetch(`${UDDOKTAPAY_BASE_URL}/checkout-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
      },
      body: JSON.stringify(paymentData),
    });

    const paymentResponse = await response.json();
    
    if (!response.ok || !paymentResponse.status) {
      throw new Error('Payment initialization failed: ' + (paymentResponse.message || 'Unknown error'));
    }

    console.log('✅ Payment initialized successfully!');
    console.log(`🔗 Payment URL: ${paymentResponse.payment_url}`);
    console.log(`📄 Invoice ID: ${paymentResponse.invoice_id || invoiceId}`);

    // Step 4: Store payment record
    console.log('\n4️⃣ Storing payment record...');
    
    const { error: paymentRecordError } = await supabase
      .from('payment_records')
      .insert({
        invoice_id: paymentResponse.invoice_id || invoiceId,
        user_id: testUserId,
        booking_id: booking.id,
        booking_type: 'venue',
        amount: timeSlot.price,
        currency: 'BDT',
        status: 'pending',
        payment_url: paymentResponse.payment_url,
        uddoktapay_response: paymentResponse,
      });

    if (paymentRecordError) {
      console.warn('⚠️ Failed to store payment record:', paymentRecordError.message);
    } else {
      console.log('✅ Payment record stored successfully');
    }

    // Step 5: Test payment verification (will fail since not paid)
    console.log('\n5️⃣ Testing payment verification...');
    
    try {
      const verifyResponse = await fetch(`${UDDOKTAPAY_BASE_URL}/verify-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'RT-UDDOKTAPAY-API-KEY': UDDOKTAPAY_API_KEY,
        },
        body: JSON.stringify({
          invoice_id: paymentResponse.invoice_id || invoiceId
        }),
      });

      const verifyData = await verifyResponse.json();
      console.log('✅ Payment verification API working');
      console.log('📊 Payment status:', verifyData.status || 'PENDING');
    } catch (verifyError) {
      console.log('⚠️ Payment verification failed (expected for unpaid invoice)');
    }

    console.log('\n🎉 Complete payment flow test successful!');
    console.log('\n📱 You can now test the payment flow in the app:');
    console.log('1. Go to http://localhost:8082');
    console.log('2. Browse venues');
    console.log('3. Select a time slot');
    console.log('4. Proceed to payment');
    console.log('5. Click on any payment method to test UddoktaPay');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testCompletePaymentFlow();
