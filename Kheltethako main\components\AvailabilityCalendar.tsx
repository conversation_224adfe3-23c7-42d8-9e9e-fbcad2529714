import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Calendar, Clock, CheckCircle, XCircle } from 'lucide-react-native';
import { getTimeSlotAvailability } from '@/services/supabaseApi';

interface TimeSlot {
  id: string;
  start_time: string;
  end_time: string;
  is_available: boolean;
  price: number;
  booking_id?: string;
}

interface AvailabilityCalendarProps {
  venueId?: number;
  coachId?: number;
  selectedDate: string;
  onTimeSlotSelect: (timeSlot: TimeSlot) => void;
  selectedTimeSlot?: TimeSlot;
}

export default function AvailabilityCalendar({
  venueId,
  coachId,
  selectedDate,
  onTimeSlotSelect,
  selectedTimeSlot,
}: AvailabilityCalendarProps) {
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchAvailability();
  }, [venueId, coachId, selectedDate]);

  const fetchAvailability = async () => {
    setIsLoading(true);
    try {
      // Get real-time availability from Supabase
      const slots = await getTimeSlotAvailability({
        venue_id: venueId,
        coach_id: coachId,
        booking_date: selectedDate,
      });
      setTimeSlots(slots);
    } catch (error) {
      console.error('Error fetching availability:', error);
      // Fallback to empty array on error
      setTimeSlots([]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':');
    const hourNum = parseInt(hour);
    const ampm = hourNum >= 12 ? 'PM' : 'AM';
    const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
    return `${displayHour}:${minute} ${ampm}`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0B8457" />
        <Text style={styles.loadingText}>Loading availability...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Calendar size={20} color="#0B8457" />
        <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
      </View>

      <ScrollView style={styles.slotsContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.slotsGrid}>
          {timeSlots.map((slot) => (
            <TouchableOpacity
              key={slot.id}
              style={[
                styles.timeSlot,
                !slot.is_available && styles.timeSlotUnavailable,
                selectedTimeSlot?.id === slot.id && styles.timeSlotSelected,
              ]}
              onPress={() => slot.is_available && onTimeSlotSelect(slot)}
              disabled={!slot.is_available}
            >
              <View style={styles.timeSlotHeader}>
                <Text style={[
                  styles.timeSlotTime,
                  !slot.is_available && styles.timeSlotTimeUnavailable,
                  selectedTimeSlot?.id === slot.id && styles.timeSlotTimeSelected,
                ]}>
                  {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
                </Text>
                {slot.is_available ? (
                  <CheckCircle size={16} color="#10B981" />
                ) : (
                  <XCircle size={16} color="#EF4444" />
                )}
              </View>
              
              <Text style={[
                styles.timeSlotPrice,
                !slot.is_available && styles.timeSlotPriceUnavailable,
                selectedTimeSlot?.id === slot.id && styles.timeSlotPriceSelected,
              ]}>
                {slot.is_available ? `৳${slot.price}` : 'Booked'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <CheckCircle size={16} color="#10B981" />
          <Text style={styles.legendText}>Available</Text>
        </View>
        <View style={styles.legendItem}>
          <XCircle size={16} color="#EF4444" />
          <Text style={styles.legendText}>Booked</Text>
        </View>
        <View style={styles.legendItem}>
          <Clock size={16} color="#0B8457" />
          <Text style={styles.legendText}>Selected</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dateText: {
    marginLeft: 8,
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  slotsContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  slotsGrid: {
    paddingVertical: 16,
    gap: 12,
  },
  timeSlot: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  timeSlotUnavailable: {
    backgroundColor: '#F9FAFB',
    borderColor: '#D1D5DB',
  },
  timeSlotSelected: {
    backgroundColor: '#ECFDF5',
    borderColor: '#0B8457',
  },
  timeSlotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeSlotTime: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  timeSlotTimeUnavailable: {
    color: '#9CA3AF',
  },
  timeSlotTimeSelected: {
    color: '#0B8457',
  },
  timeSlotPrice: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
  },
  timeSlotPriceUnavailable: {
    color: '#9CA3AF',
  },
  timeSlotPriceSelected: {
    color: '#0B8457',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendText: {
    marginLeft: 6,
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
});
