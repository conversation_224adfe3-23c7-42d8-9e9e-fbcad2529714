import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Linking,
  ScrollView,
  Platform,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as WebBrowser from 'expo-web-browser';
import { ArrowLeft, CreditCard, Lock, Smartphone, Calendar, Clock, MapPin } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { initializePayment } from '@/services/paymentService';
import { createBooking } from '@/services/supabaseApi';

export default function PaymentScreen() {
  const params = useLocalSearchParams();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [bookingData, setBookingData] = useState(null);

  // Parse booking data from params
  useEffect(() => {
    if (params.timeSlot && !bookingData) {
      try {
        const timeSlot = JSON.parse(params.timeSlot as string);
        const totalAmount = parseInt(params.totalAmount as string) || 2000;

        setBookingData({
          venueId: params.venueId,
          venueName: params.venueName,
          coachId: params.coachId,
          coachName: params.coachName,
          date: params.date,
          timeSlot: timeSlot,
          totalAmount: totalAmount,
          type: params.type || 'venue',
        });
      } catch (error) {
        console.error('Error parsing booking data:', error);
      }
    }
  }, [
    params.timeSlot,
    params.totalAmount,
    params.venueId,
    params.venueName,
    params.coachId,
    params.coachName,
    params.date,
    params.type,
    bookingData
  ]);

  const handleUddoktaPayPayment = async () => {
    console.log('🚀 Starting UddoktaPay payment process...');
    console.log('User:', user);
    console.log('Booking Data:', bookingData);

    if (!user) {
      console.error('❌ No user found');
      Alert.alert('Authentication Error', 'Please log in to continue with payment.');
      return;
    }

    if (!bookingData) {
      console.error('❌ No booking data found');
      Alert.alert('Booking Error', 'Booking information is missing. Please try again.');
      return;
    }

    try {
      setIsLoading(true);

      // First create the booking in our database
      const booking = await createBooking({
        user_id: user.id,
        venue_id: bookingData.type === 'venue' ? parseInt(bookingData.venueId as string) : undefined,
        coach_id: bookingData.type === 'coach' ? parseInt(bookingData.coachId as string) : undefined,
        booking_date: bookingData.date,
        start_time: bookingData.timeSlot.start_time,
        end_time: bookingData.timeSlot.end_time,
        duration_hours: 1, // Calculate based on time slot
        total_players: bookingData.type === 'venue' ? 10 : 1, // Default or get from form
        price_per_hour: bookingData.timeSlot.price,
        total_amount: bookingData.totalAmount,
      });

      if (!booking) {
        throw new Error('Failed to create booking');
      }

      console.log('✅ Booking created successfully:', booking);

      // Initialize payment with UddoktaPay
      const paymentRequest = {
        amount: bookingData.totalAmount,
        currency: 'BDT',
        booking_id: booking.id.toString(),
        user_id: user.id,
        booking_type: bookingData.type === 'venue' ? 'venue' : 'coach',
        customer_name: user.full_name || user.name || 'User',
        customer_email: user.email,
        customer_phone: user.phone,
        success_url: `http://localhost:8082/payment-success?booking_id=${booking.id}`,
        cancel_url: `http://localhost:8082/payment-cancel?booking_id=${booking.id}`,
      };

      console.log('💳 Initializing payment with:', paymentRequest);

      const paymentResponse = await initializePayment(paymentRequest);

      console.log('💰 Payment response:', paymentResponse);

      if (paymentResponse.success && paymentResponse.payment_url) {
        console.log('🔗 Opening payment URL:', paymentResponse.payment_url);

        // For web, open in new tab. For mobile, use in-app browser
        if (Platform.OS === 'web') {
          // Web environment - open in new tab
          window.open(paymentResponse.payment_url, '_blank');
          console.log('✅ Payment URL opened in new tab (web)');
        } else {
          // Mobile environment - use in-app browser
          try {
            console.log('📱 Opening payment in in-app browser (mobile)');
            const result = await WebBrowser.openBrowserAsync(paymentResponse.payment_url, {
              presentationStyle: WebBrowser.WebBrowserPresentationStyle.FULL_SCREEN,
              controlsColor: '#0B8457',
              toolbarColor: '#FFFFFF',
              secondaryToolbarColor: '#F9FAFB',
              showTitle: true,
              enableBarCollapsing: false,
              showInRecents: true,
            });

            console.log('✅ In-app browser result:', result);

            // Handle the result when user returns from payment
            if (result.type === 'cancel') {
              console.log('🔙 User cancelled payment');
              Alert.alert('Payment Cancelled', 'You can try again anytime.');
            } else if (result.type === 'dismiss') {
              console.log('🔙 User dismissed payment browser');
              // Check if payment was successful
              Alert.alert(
                'Payment Status',
                'Please wait while we verify your payment...',
                [{ text: 'OK' }]
              );
              // You can add payment verification logic here
              // For now, we'll navigate to a success page or show status
            }
          } catch (error) {
            console.error('❌ Error opening in-app browser:', error);
            // Fallback to external browser
            const supported = await Linking.canOpenURL(paymentResponse.payment_url);
            if (supported) {
              await Linking.openURL(paymentResponse.payment_url);
              console.log('✅ Fallback: Payment URL opened in external browser');
            } else {
              console.error('❌ Cannot open payment URL');
              Alert.alert('Error', 'Cannot open payment page');
            }
          }
        }
      } else {
        console.error('❌ Payment initialization failed:', paymentResponse.error);
        throw new Error(paymentResponse.error || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Payment Error', error.message || 'Failed to process payment');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMobilePayment = async (method: string) => {
    console.log(`🔘 ${method} payment button clicked`);

    if (isLoading) {
      console.log('⏳ Payment already in progress, ignoring click');
      return;
    }

    // For web, directly process payment without Alert
    if (Platform.OS === 'web') {
      console.log('🌐 Web environment detected, processing payment directly');
      try {
        await handleUddoktaPayPayment();
      } catch (error) {
        console.error('❌ Payment processing failed:', error);
        Alert.alert('Payment Error', 'Failed to process payment. Please try again.');
      }
    } else {
      // For mobile, show confirmation alert then process with in-app browser
      Alert.alert(
        `${method} Payment`,
        `You will be redirected to ${method} payment page within the app.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Continue', onPress: handleUddoktaPayPayment },
        ]
      );
    }
  };

  if (!bookingData) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={20} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Payment</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0B8457" />
          <Text style={styles.loadingText}>Loading booking details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Payment</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Booking Summary */}
        <View style={styles.bookingSummary}>
          <Text style={styles.sectionTitle}>Booking Summary</Text>
          <View style={styles.summaryCard}>
            <Text style={styles.venueName}>
              {bookingData.type === 'venue' ? bookingData.venueName : bookingData.coachName}
            </Text>
            <View style={styles.summaryRow}>
              <Calendar size={16} color="#6B7280" />
              <Text style={styles.summaryText}>
                {new Date(bookingData.date).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Clock size={16} color="#6B7280" />
              <Text style={styles.summaryText}>
                {bookingData.timeSlot.start_time} - {bookingData.timeSlot.end_time}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <MapPin size={16} color="#6B7280" />
              <Text style={styles.summaryText}>Dhaka, Bangladesh</Text>
            </View>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethods}>
          <Text style={styles.sectionTitle}>Choose Payment Method</Text>

          {/* Mobile Banking */}
          <View style={styles.methodSection}>
            <Text style={styles.methodSectionTitle}>Mobile Banking</Text>

            <TouchableOpacity
              style={[styles.paymentOption, isLoading && styles.paymentOptionDisabled]}
              onPress={() => handleMobilePayment('bKash')}
              disabled={isLoading}
            >
              <View style={styles.paymentOptionLeft}>
                <View style={[styles.paymentIcon, { backgroundColor: '#E91E63' }]}>
                  {isLoading ? (
                    <ActivityIndicator size="small" color="#FFFFFF" />
                  ) : (
                    <Smartphone size={20} color="#FFFFFF" />
                  )}
                </View>
                <Text style={[styles.paymentOptionText, isLoading && styles.paymentOptionTextDisabled]}>
                  bKash
                </Text>
              </View>
              <Text style={styles.paymentOptionArrow}>
                {isLoading ? '⏳' : '→'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.paymentOption}
              onPress={() => handleMobilePayment('Nagad')}
              disabled={isLoading}
            >
              <View style={styles.paymentOptionLeft}>
                <View style={[styles.paymentIcon, { backgroundColor: '#FF6B35' }]}>
                  <Smartphone size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.paymentOptionText}>Nagad</Text>
              </View>
              <Text style={styles.paymentOptionArrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.paymentOption}
              onPress={() => handleMobilePayment('Rocket')}
              disabled={isLoading}
            >
              <View style={styles.paymentOptionLeft}>
                <View style={[styles.paymentIcon, { backgroundColor: '#8E44AD' }]}>
                  <Smartphone size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.paymentOptionText}>Rocket</Text>
              </View>
              <Text style={styles.paymentOptionArrow}>→</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.paymentOption}
              onPress={() => handleMobilePayment('Upay')}
              disabled={isLoading}
            >
              <View style={styles.paymentOptionLeft}>
                <View style={[styles.paymentIcon, { backgroundColor: '#2ECC71' }]}>
                  <Smartphone size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.paymentOptionText}>Upay</Text>
              </View>
              <Text style={styles.paymentOptionArrow}>→</Text>
            </TouchableOpacity>
          </View>

          {/* Bank Transfer */}
          <View style={styles.methodSection}>
            <Text style={styles.methodSectionTitle}>Bank Transfer</Text>

            <TouchableOpacity
              style={styles.paymentOption}
              onPress={() => handleMobilePayment('Bank Transfer')}
              disabled={isLoading}
            >
              <View style={styles.paymentOptionLeft}>
                <View style={[styles.paymentIcon, { backgroundColor: '#3498DB' }]}>
                  <CreditCard size={20} color="#FFFFFF" />
                </View>
                <Text style={styles.paymentOptionText}>Bank Transfer</Text>
              </View>
              <Text style={styles.paymentOptionArrow}>→</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Security Note */}
        <View style={styles.secureNote}>
          <Lock size={16} color="#6B7280" />
          <Text style={styles.secureText}>
            Your payment is secured by UddoktaPay with 256-bit SSL encryption
          </Text>
        </View>
      </ScrollView>

      {/* Footer with Total */}
      <View style={styles.footer}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Total Amount</Text>
          <Text style={styles.totalAmount}>৳{bookingData.totalAmount}</Text>
        </View>

        {isLoading && (
          <View style={styles.processingContainer}>
            <ActivityIndicator color="#0B8457" />
            <Text style={styles.processingText}>Processing payment...</Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginLeft: 12,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  bookingSummary: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  summaryCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  venueName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  paymentMethods: {
    marginBottom: 24,
  },
  methodSection: {
    marginBottom: 20,
  },
  methodSectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  paymentOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  paymentOptionDisabled: {
    opacity: 0.6,
    backgroundColor: '#F9FAFB',
  },
  paymentOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentOptionText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  paymentOptionTextDisabled: {
    color: '#9CA3AF',
  },
  paymentOptionArrow: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#6B7280',
  },
  secureNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  secureText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 8,
    flex: 1,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  totalAmount: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#0B8457',
  },
  processingContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  processingText: {
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
  },
});