const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add support for TypeScript and other file extensions
config.resolver.sourceExts.push('cjs');

// Configure web support with platform-specific extensions
config.resolver.platforms = ['web', 'ios', 'android', 'native'];

// Add support for SVG (only if react-native-svg-transformer is available)
try {
  config.transformer.babelTransformerPath = require.resolve('react-native-svg-transformer');
  config.resolver.assetExts = config.resolver.assetExts.filter((ext) => ext !== 'svg');
  config.resolver.sourceExts.push('svg');
} catch (error) {
  console.log('SVG transformer not available, skipping SVG support');
}

module.exports = config;
