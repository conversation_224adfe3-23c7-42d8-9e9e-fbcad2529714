import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { User, CreditCard, Map, Heart, Settings, CircleHelp as HelpCircle, LogOut, ChevronRight, Moon, Bell, Calendar, Shield } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';

const ProfileItem = ({ icon, title, onPress, showBadge = false, badge = null }) => (
  <TouchableOpacity style={styles.profileItem} onPress={onPress}>
    <View style={styles.profileItemLeft}>
      {icon}
      <Text style={styles.profileItemText}>{title}</Text>
    </View>
    <View style={styles.profileItemRight}>
      {showBadge && <View style={styles.badge}><Text style={styles.badgeText}>{badge}</Text></View>}
      <ChevronRight size={20} color="#6B7280" />
    </View>
  </TouchableOpacity>
);

export default function ProfileScreen() {
  const { user, signOut } = useAuth();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          onPress: async () => {
            await signOut();
            // Stay on the same screen but show login prompt
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  // Show login prompt for unauthenticated users
  if (!user) {
    return (
      <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
        <View style={styles.header}>
          <Text style={styles.title}>Profile</Text>
        </View>

        <View style={styles.loginPromptContainer}>
          <View style={styles.loginPromptContent}>
            <User size={64} color="#0B8457" style={styles.loginIcon} />
            <Text style={styles.loginPromptTitle}>Sign in to your account</Text>
            <Text style={styles.loginPromptSubtitle}>
              Access your bookings, favorites, and personalized recommendations
            </Text>

            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>Sign In</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.registerButton} onPress={handleRegister}>
              <Text style={styles.registerButtonText}>Create Account</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Public settings that don't require authentication */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <View style={styles.preferenceItem}>
            <View style={styles.preferenceItemLeft}>
              <Moon size={22} color="#0B8457" />
              <Text style={styles.preferenceItemText}>Dark Mode</Text>
            </View>
            <Switch
              trackColor={{ false: '#D1D5DB', true: '#0B8457' }}
              thumbColor="#FFFFFF"
              ios_backgroundColor="#D1D5DB"
              onValueChange={setIsDarkMode}
              value={isDarkMode}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <ProfileItem
            icon={<HelpCircle size={22} color="#0B8457" />}
            title="Help Center"
            onPress={() => router.push('/help')}
          />
          <ProfileItem
            icon={<Settings size={22} color="#0B8457" />}
            title="Settings"
            onPress={() => router.push('/settings')}
          />
        </View>

        <View style={styles.footer}>
          <Text style={styles.version}>Version 1.0.0</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.title}>My Profile</Text>
        </View>

        <View style={styles.profileHeader}>
          <Image
            source={{
              uri: user?.avatar_url || 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
            }}
            style={styles.profileImage}
          />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{user?.name || 'Guest User'}</Text>
            <Text style={styles.profileEmail}>{user?.email || '<EMAIL>'}</Text>
            <View style={styles.profileTypeContainer}>
              <Text style={styles.profileType}>
                {user?.user_type === 'venue_owner'
                  ? 'Venue Owner'
                  : user?.user_type === 'coach'
                  ? 'Coach'
                  : 'Player'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => router.push('/edit-profile')}
          >
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <ProfileItem
            icon={<User size={22} color="#0B8457" />}
            title="Personal Information"
            onPress={() => router.push('/personal-info')}
          />
          <ProfileItem
            icon={<CreditCard size={22} color="#0B8457" />}
            title="Payment Methods"
            onPress={() => router.push('/payment-methods')}
          />
          <ProfileItem
            icon={<Map size={22} color="#0B8457" />}
            title="Saved Locations"
            onPress={() => router.push('/saved-locations')}
          />
          <ProfileItem
            icon={<Heart size={22} color="#0B8457" />}
            title="Favorites"
            onPress={() => router.push('/favorites')}
            showBadge={true}
            badge="5"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Activity</Text>
          <ProfileItem
            icon={<Calendar size={22} color="#0B8457" />}
            title="Booking History"
            onPress={() => router.push('/booking-history')}
          />
          <ProfileItem
            icon={<Bell size={22} color="#0B8457" />}
            title="Notifications"
            onPress={() => router.push('/notifications')}
            showBadge={true}
            badge="3"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <View style={styles.preferenceItem}>
            <View style={styles.preferenceItemLeft}>
              <Moon size={22} color="#0B8457" />
              <Text style={styles.preferenceItemText}>Dark Mode</Text>
            </View>
            <Switch
              trackColor={{ false: '#D1D5DB', true: '#0B8457' }}
              thumbColor="#FFFFFF"
              ios_backgroundColor="#D1D5DB"
              onValueChange={setIsDarkMode}
              value={isDarkMode}
            />
          </View>
          <View style={styles.preferenceItem}>
            <View style={styles.preferenceItemLeft}>
              <Bell size={22} color="#0B8457" />
              <Text style={styles.preferenceItemText}>Notifications</Text>
            </View>
            <Switch
              trackColor={{ false: '#D1D5DB', true: '#0B8457' }}
              thumbColor="#FFFFFF"
              ios_backgroundColor="#D1D5DB"
              onValueChange={setNotificationsEnabled}
              value={notificationsEnabled}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          <ProfileItem
            icon={<Shield size={22} color="#0B8457" />}
            title="Privacy Settings"
            onPress={() => router.push('/privacy-settings')}
          />
          <ProfileItem
            icon={<HelpCircle size={22} color="#0B8457" />}
            title="Help Center"
            onPress={() => router.push('/help')}
          />
          <ProfileItem
            icon={<Settings size={22} color="#0B8457" />}
            title="Settings"
            onPress={() => router.push('/settings')}
          />
        </View>

        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <LogOut size={22} color="#EF4444" />
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.version}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 22,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  profileHeader: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  profileImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  profileInfo: {
    flex: 1,
    marginLeft: 16,
  },
  profileName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 6,
  },
  profileTypeContainer: {
    backgroundColor: '#ECFDF5',
    paddingVertical: 2,
    paddingHorizontal: 8,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  profileType: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#059669',
  },
  editButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#0B8457',
  },
  editButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
  },
  section: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 12,
  },
  profileItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileItemText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    marginLeft: 12,
  },
  profileItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    backgroundColor: '#0B8457',
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    paddingHorizontal: 4,
  },
  preferenceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  preferenceItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  preferenceItemText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    marginLeft: 12,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginTop: 20,
    marginHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FEE2E2',
    backgroundColor: '#FEF2F2',
  },
  signOutText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
    marginLeft: 8,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  version: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  loginPromptContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  loginPromptContent: {
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 16,
    padding: 32,
    marginBottom: 20,
  },
  loginIcon: {
    marginBottom: 16,
  },
  loginPromptTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 8,
  },
  loginPromptSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  loginButton: {
    backgroundColor: '#0B8457',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    marginBottom: 12,
    width: '100%',
  },
  loginButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  registerButton: {
    backgroundColor: 'transparent',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0B8457',
    width: '100%',
  },
  registerButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
    textAlign: 'center',
  },
});