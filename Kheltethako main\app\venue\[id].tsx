import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  FlatList,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Heart, MapPin, Star, Users, Calendar, Clock, Share2, CircleCheck as CheckCircle2 } from 'lucide-react-native';
import { getVenueDetails } from '@/services/supabaseApi';
import { useAuth } from '@/contexts/AuthContext';
import { requireAuthWithCallback } from '@/utils/auth';
import AvailabilityCalendar from '@/components/AvailabilityCalendar';

const { width } = Dimensions.get('window');

export default function VenueDetailsScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuth();
  const [venue, setVenue] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAvailability, setShowAvailability] = useState(false);

  useEffect(() => {
    fetchVenueDetails();
  }, [id]);

  const fetchVenueDetails = async () => {
    try {
      setIsLoading(true);
      const data = await getVenueDetails(id);
      setVenue(data);
      
      // Set first available date as default selected
      if (data.available_dates && data.available_dates.length > 0) {
        setSelectedDate(data.available_dates[0]);
      }
    } catch (error) {
      console.error('Error fetching venue details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleFavorite = () => {
    requireAuthWithCallback(
      user,
      () => setIsFavorite(!isFavorite),
      'add venues to favorites'
    );
  };

  const handleTimeSlotSelect = (timeSlot) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleBookNow = () => {
    if (!selectedDate || !selectedTimeSlot) {
      Alert.alert('Select Time Slot', 'Please select a date and time slot to continue booking.');
      return;
    }

    requireAuthWithCallback(
      user,
      () => {
        router.push({
          pathname: '/booking-confirmation',
          params: {
            venueId: id,
            venueName: venue?.name,
            date: selectedDate,
            timeSlot: JSON.stringify(selectedTimeSlot),
            price: selectedTimeSlot.price,
          },
        });
      },
      'book this venue'
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0B8457" />
      </SafeAreaView>
    );
  }

  if (!venue) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>Venue not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <FlatList
            data={venue.images}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <Image source={{ uri: item }} style={styles.venueImage} />
            )}
          />
          <View style={styles.imageOverlay}>
            <TouchableOpacity
              style={styles.backButtonCircle}
              onPress={() => router.back()}
            >
              <ArrowLeft size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={toggleFavorite}
            >
              <Heart
                size={20}
                color="#FFFFFF"
                fill={isFavorite ? '#EF4444' : 'transparent'}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.venueName}>{venue.name}</Text>
          <View style={styles.venueLocation}>
            <MapPin size={16} color="#6B7280" />
            <Text style={styles.venueLocationText}>{venue.location}</Text>
          </View>

          <View style={styles.ratingContainer}>
            <View style={styles.rating}>
              <Star size={16} color="#F59E0B" fill="#F59E0B" />
              <Text style={styles.ratingText}>
                {venue.rating} ({venue.reviews_count} reviews)
              </Text>
            </View>
            <TouchableOpacity style={styles.shareButton}>
              <Share2 size={16} color="#6B7280" />
              <Text style={styles.shareText}>Share</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Users size={16} color="#0B8457" />
              <Text style={styles.infoText}>
                Capacity: {venue.capacity} people
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Clock size={16} color="#0B8457" />
              <Text style={styles.infoText}>
                ৳{venue.price_per_hour}/hour
              </Text>
            </View>
          </View>

          <View style={styles.divider} />

          <Text style={styles.sectionTitle}>About</Text>
          <Text style={styles.description}>{venue.description}</Text>

          <View style={styles.featuresContainer}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            <View style={styles.featuresList}>
              {venue.amenities.map((amenity, index) => (
                <View key={index} style={styles.featureItem}>
                  <CheckCircle2 size={16} color="#0B8457" />
                  <Text style={styles.featureText}>{amenity}</Text>
                </View>
              ))}
            </View>
          </View>

          <View style={styles.divider} />

          <View style={styles.bookingSection}>
            <Text style={styles.sectionTitle}>Book Your Session</Text>
            <TouchableOpacity
              style={styles.availabilityButton}
              onPress={() => setShowAvailability(!showAvailability)}
            >
              <Calendar size={20} color="#0B8457" />
              <Text style={styles.availabilityButtonText}>
                {selectedDate ? `Selected: ${new Date(selectedDate).toLocaleDateString()}` : 'Select Date & Time'}
              </Text>
              {selectedTimeSlot && (
                <Text style={styles.selectedTimeText}>
                  {selectedTimeSlot.start_time} - {selectedTimeSlot.end_time}
                </Text>
              )}
            </TouchableOpacity>

            {showAvailability && (
              <View style={styles.availabilityContainer}>
                <AvailabilityCalendar
                  venueId={parseInt(id as string)}
                  selectedDate={selectedDate}
                  onTimeSlotSelect={handleTimeSlotSelect}
                  selectedTimeSlot={selectedTimeSlot}
                />
              </View>
            )}
          </View>

          <View style={styles.divider} />

          <Text style={styles.sectionTitle}>Reviews</Text>
          <TouchableOpacity
            style={styles.viewAllReviews}
            onPress={() => router.push(`/venue/${id}/reviews`)}
          >
            <Text style={styles.viewAllReviewsText}>
              View all {venue.reviews_count} reviews
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceText}>Total Price</Text>
          <Text style={styles.price}>
            ৳{venue.price_per_hour} <Text style={styles.perHour}>/hour</Text>
          </Text>
        </View>
        <TouchableOpacity
          style={[
            styles.bookButton,
            (!selectedDate || !selectedTimeSlot) && styles.bookButtonDisabled,
          ]}
          onPress={handleBookNow}
          disabled={!selectedDate || !selectedTimeSlot}
        >
          <Text style={styles.bookButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
    marginBottom: 16,
  },
  backButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: '#0B8457',
    borderRadius: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  venueImage: {
    width,
    height: 300,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  backButtonCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  favoriteButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 20,
  },
  venueName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 8,
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  venueLocationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 6,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shareText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 6,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginLeft: 6,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    lineHeight: 22,
    marginBottom: 20,
  },
  featuresContainer: {
    marginBottom: 20,
  },
  featuresList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    paddingVertical: 6,
  },
  featureText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#4B5563',
    marginLeft: 8,
  },
  dateContainer: {
    paddingVertical: 10,
    marginBottom: 20,
  },
  dateItem: {
    width: 60,
    height: 80,
    borderRadius: 8,
    marginRight: 12,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dateItemSelected: {
    backgroundColor: '#0B8457',
  },
  dateDay: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 2,
  },
  dateMonth: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  dateTextSelected: {
    color: '#FFFFFF',
  },
  timeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  timeItem: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
    marginRight: 10,
    marginBottom: 10,
  },
  timeItemSelected: {
    backgroundColor: '#0B8457',
  },
  timeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#4B5563',
  },
  timeTextSelected: {
    color: '#FFFFFF',
  },
  viewAllReviews: {
    paddingVertical: 10,
  },
  viewAllReviewsText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#0B8457',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  priceContainer: {
    flex: 1,
  },
  priceText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 4,
  },
  price: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  perHour: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  bookButton: {
    backgroundColor: '#0B8457',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  bookButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  bookButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  bookingSection: {
    marginBottom: 20,
  },
  availabilityButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  availabilityButtonText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#111827',
  },
  selectedTimeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#0B8457',
  },
  availabilityContainer: {
    marginTop: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
    height: 400,
  },
});