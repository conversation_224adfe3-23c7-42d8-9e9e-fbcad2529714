// Analytics API Service for KhelteThako
import { supabase } from '@/lib/supabase';

export interface PaymentAnalyticsSummary {
  total_transactions: number;
  successful_transactions: number;
  failed_transactions: number;
  success_rate: number;
  total_revenue: number;
  total_fees: number;
  net_revenue: number;
  avg_transaction_value: number;
  top_payment_method: string;
  venue_vs_coach_ratio: number;
}

export interface HourlyTrend {
  hour: number;
  transactions: number;
  revenue: number;
  success_rate: number;
}

export interface PaymentMethodBreakdown {
  payment_method: string;
  transactions: number;
  percentage: number;
}

export interface DailyRevenueTrend {
  date: string;
  total_revenue: number;
  net_revenue: number;
  transactions: number;
  avg_transaction_value: number;
}

export interface RealtimeMetrics {
  today_revenue: number;
  today_transactions: number;
  active_bookings: number;
  pending_payments: number;
  success_rate_today: number;
}

/**
 * Get payment analytics summary for a date range
 */
export const getPaymentAnalyticsSummary = async (
  startDate?: string,
  endDate?: string
): Promise<PaymentAnalyticsSummary | null> => {
  try {
    const { data, error } = await supabase.rpc('get_payment_analytics_summary', {
      start_date: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end_date: endDate || new Date().toISOString().split('T')[0]
    });

    if (error) {
      console.error('Error fetching analytics summary:', error);
      return null;
    }

    return data[0] || null;
  } catch (error) {
    console.error('Error in getPaymentAnalyticsSummary:', error);
    return null;
  }
};

/**
 * Get hourly payment trends for a specific date
 */
export const getHourlyPaymentTrends = async (
  date?: string
): Promise<HourlyTrend[]> => {
  try {
    const { data, error } = await supabase.rpc('get_hourly_payment_trends', {
      target_date: date || new Date().toISOString().split('T')[0]
    });

    if (error) {
      console.error('Error fetching hourly trends:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getHourlyPaymentTrends:', error);
    return [];
  }
};

/**
 * Get payment method breakdown for a date range
 */
export const getPaymentMethodBreakdown = async (
  startDate?: string,
  endDate?: string
): Promise<PaymentMethodBreakdown[]> => {
  try {
    const { data, error } = await supabase.rpc('get_payment_method_breakdown', {
      start_date: startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end_date: endDate || new Date().toISOString().split('T')[0]
    });

    if (error) {
      console.error('Error fetching payment method breakdown:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getPaymentMethodBreakdown:', error);
    return [];
  }
};

/**
 * Get daily revenue trends for a date range
 */
export const getDailyRevenueTrends = async (
  startDate?: string,
  endDate?: string
): Promise<DailyRevenueTrend[]> => {
  try {
    const { data, error } = await supabase.rpc('get_daily_revenue_trends', {
      start_date: startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end_date: endDate || new Date().toISOString().split('T')[0]
    });

    if (error) {
      console.error('Error fetching daily revenue trends:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getDailyRevenueTrends:', error);
    return [];
  }
};

/**
 * Get real-time metrics for dashboard
 */
export const getRealtimeMetrics = async (): Promise<RealtimeMetrics> => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // Get today's analytics
    const { data: todayAnalytics, error: analyticsError } = await supabase
      .from('payment_analytics')
      .select('*')
      .eq('date', today);

    if (analyticsError) {
      console.error('Error fetching today analytics:', analyticsError);
    }

    // Aggregate today's data
    const todayData = todayAnalytics?.reduce((acc, curr) => ({
      total_revenue: acc.total_revenue + (curr.total_revenue || 0),
      total_transactions: acc.total_transactions + (curr.total_transactions || 0),
      successful_transactions: acc.successful_transactions + (curr.successful_transactions || 0),
    }), { total_revenue: 0, total_transactions: 0, successful_transactions: 0 }) || 
    { total_revenue: 0, total_transactions: 0, successful_transactions: 0 };

    // Get active bookings count
    const { count: activeBookings } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'confirmed')
      .gte('booking_date', today);

    // Get pending payments count
    const { count: pendingPayments } = await supabase
      .from('payment_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    const successRate = todayData.total_transactions > 0 
      ? (todayData.successful_transactions / todayData.total_transactions) * 100 
      : 0;

    return {
      today_revenue: todayData.total_revenue,
      today_transactions: todayData.total_transactions,
      active_bookings: activeBookings || 0,
      pending_payments: pendingPayments || 0,
      success_rate_today: Math.round(successRate * 100) / 100,
    };
  } catch (error) {
    console.error('Error in getRealtimeMetrics:', error);
    return {
      today_revenue: 0,
      today_transactions: 0,
      active_bookings: 0,
      pending_payments: 0,
      success_rate_today: 0,
    };
  }
};

/**
 * Get top performing venues by revenue
 */
export const getTopVenuesByRevenue = async (
  startDate?: string,
  endDate?: string,
  limit: number = 10
): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_transactions')
      .select(`
        metadata,
        charged_amount,
        bookings!inner(
          venue_id,
          venues!inner(
            id,
            name,
            images
          )
        )
      `)
      .eq('status', 'completed')
      .gte('created_at', startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      .lte('created_at', endDate || new Date().toISOString());

    if (error) {
      console.error('Error fetching top venues:', error);
      return [];
    }

    // Group by venue and calculate revenue
    const venueRevenue = data?.reduce((acc, transaction) => {
      const venue = transaction.bookings?.venues;
      if (venue) {
        const venueId = venue.id;
        if (!acc[venueId]) {
          acc[venueId] = {
            venue_id: venueId,
            venue_name: venue.name,
            venue_image: venue.images?.[0] || null,
            total_revenue: 0,
            booking_count: 0,
          };
        }
        acc[venueId].total_revenue += parseFloat(transaction.charged_amount || 0);
        acc[venueId].booking_count += 1;
      }
      return acc;
    }, {}) || {};

    return Object.values(venueRevenue)
      .sort((a: any, b: any) => b.total_revenue - a.total_revenue)
      .slice(0, limit);
  } catch (error) {
    console.error('Error in getTopVenuesByRevenue:', error);
    return [];
  }
};

/**
 * Get user engagement metrics
 */
export const getUserEngagementMetrics = async (
  startDate?: string,
  endDate?: string
): Promise<any> => {
  try {
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
    const end = endDate || new Date().toISOString();

    // Get new users
    const { count: newUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', start)
      .lte('created_at', end);

    // Get active users (users who made bookings)
    const { data: activeUsersData } = await supabase
      .from('bookings')
      .select('user_id')
      .gte('created_at', start)
      .lte('created_at', end);

    const activeUsers = new Set(activeUsersData?.map(b => b.user_id) || []).size;

    // Get repeat customers (users with more than 1 booking)
    const userBookingCounts = activeUsersData?.reduce((acc, booking) => {
      acc[booking.user_id] = (acc[booking.user_id] || 0) + 1;
      return acc;
    }, {}) || {};

    const repeatCustomers = Object.values(userBookingCounts).filter(count => count > 1).length;

    return {
      new_users: newUsers || 0,
      active_users: activeUsers,
      repeat_customers: repeatCustomers,
      retention_rate: activeUsers > 0 ? Math.round((repeatCustomers / activeUsers) * 100) : 0,
    };
  } catch (error) {
    console.error('Error in getUserEngagementMetrics:', error);
    return {
      new_users: 0,
      active_users: 0,
      repeat_customers: 0,
      retention_rate: 0,
    };
  }
};

/**
 * Export analytics data to CSV format
 */
export const exportAnalyticsData = async (
  type: 'payments' | 'bookings' | 'users',
  startDate?: string,
  endDate?: string
): Promise<string> => {
  try {
    let data: any[] = [];
    let headers: string[] = [];

    switch (type) {
      case 'payments':
        const { data: payments } = await supabase
          .from('payment_transactions')
          .select('*')
          .gte('created_at', startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
          .lte('created_at', endDate || new Date().toISOString())
          .order('created_at', { ascending: false });

        data = payments || [];
        headers = ['Invoice ID', 'Transaction ID', 'Amount', 'Fee', 'Charged Amount', 'Status', 'Payment Method', 'Date'];
        break;

      case 'bookings':
        const { data: bookings } = await supabase
          .from('bookings')
          .select('*')
          .gte('created_at', startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
          .lte('created_at', endDate || new Date().toISOString())
          .order('created_at', { ascending: false });

        data = bookings || [];
        headers = ['Booking Number', 'User ID', 'Venue ID', 'Date', 'Start Time', 'End Time', 'Status', 'Total Amount'];
        break;

      case 'users':
        const { data: users } = await supabase
          .from('users')
          .select('*')
          .gte('created_at', startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
          .lte('created_at', endDate || new Date().toISOString())
          .order('created_at', { ascending: false });

        data = users || [];
        headers = ['User ID', 'Email', 'Full Name', 'Phone', 'Location', 'Member Since', 'Active'];
        break;
    }

    // Convert to CSV
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const key = header.toLowerCase().replace(/ /g, '_');
          const value = row[key] || '';
          return `"${value.toString().replace(/"/g, '""')}"`;
        }).join(',')
      )
    ].join('\n');

    return csvContent;
  } catch (error) {
    console.error('Error exporting analytics data:', error);
    throw error;
  }
};

export default {
  getPaymentAnalyticsSummary,
  getHourlyPaymentTrends,
  getPaymentMethodBreakdown,
  getDailyRevenueTrends,
  getRealtimeMetrics,
  getTopVenuesByRevenue,
  getUserEngagementMetrics,
  exportAnalyticsData,
};
