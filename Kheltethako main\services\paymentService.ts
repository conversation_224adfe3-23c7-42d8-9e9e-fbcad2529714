// Payment Service for KhelteThako using UddoktaPay
import { supabase } from '@/lib/supabase';

export interface PaymentRequest {
  amount: number;
  currency?: string;
  booking_id?: string;
  session_id?: string;
  user_id: string;
  booking_type: 'venue' | 'coach';
  success_url?: string;
  cancel_url?: string;
  fail_url?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
}

export interface PaymentResponse {
  success: boolean;
  payment_url?: string;
  invoice_id?: string;
  error?: string;
}

/**
 * Initialize payment with UddoktaPay
 */
export const initializePayment = async (paymentData: PaymentRequest): Promise<PaymentResponse> => {
  try {
    const {
      amount,
      currency = 'BDT',
      booking_id,
      session_id,
      user_id,
      booking_type,
      success_url,
      cancel_url,
      fail_url,
      customer_name,
      customer_email,
      customer_phone
    } = paymentData;

    // Validate required fields
    if (!amount || amount <= 0) {
      return { success: false, error: 'Invalid amount' };
    }

    if (!user_id) {
      return { success: false, error: 'User ID is required' };
    }

    if (!booking_id && !session_id) {
      return { success: false, error: 'Booking ID or Session ID is required' };
    }

    // UddoktaPay configuration - Official API endpoints
    const uddoktaPayApiKey = 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU';
    const uddoktaPayApiUrl = 'https://digitaldot.paymently.io/api/checkout-v2';
    const webhookUrl = `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/payment-webhook`;

    if (!uddoktaPayApiKey) {
      console.error('UddoktaPay API key not configured');
      return { success: false, error: 'Payment service not configured' };
    }

    // Generate unique invoice ID
    const invoiceId = `KT_${booking_type.toUpperCase()}_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

    // Prepare UddoktaPay request according to official documentation
    const uddoktaPayRequest = {
      full_name: customer_name || 'KhelteThako User',
      email: customer_email || '<EMAIL>',
      amount: amount,
      payment_type: 'no-emi', // Standard payment type
      redirect_url: success_url || `http://localhost:8082/payment-success?booking_id=${booking_id}&invoice_id=${invoiceId}`,
      cancel_url: cancel_url || `http://localhost:8082/payment-cancel?booking_id=${booking_id}&invoice_id=${invoiceId}`,
      webhook_url: webhookUrl,
      metadata: {
        booking_id: booking_id,
        session_id: session_id,
        user_id: user_id,
        booking_type: booking_type,
        app_name: 'KhelteThako'
      }
    };

    console.log('Initializing UddoktaPay payment:', {
      invoice_id: invoiceId,
      amount: amount,
      booking_type: booking_type
    });

    // Make request to UddoktaPay
    const response = await fetch(uddoktaPayApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': uddoktaPayApiKey,
      },
      body: JSON.stringify(uddoktaPayRequest),
    });

    const responseData = await response.json();

    console.log('UddoktaPay response:', responseData);

    if (!response.ok || !responseData.status) {
      console.error('UddoktaPay API error:', responseData);
      return {
        success: false,
        error: responseData.message || 'Payment initialization failed'
      };
    }

    // Store payment record in database
    const { error: dbError } = await supabase
      .from('payment_records')
      .insert({
        invoice_id: invoiceId,
        user_id: user_id,
        booking_id: booking_id,
        session_id: session_id,
        booking_type: booking_type,
        amount: amount,
        currency: currency,
        status: 'pending',
        payment_url: responseData.payment_url,
        uddoktapay_response: responseData,
        created_at: new Date().toISOString()
      });

    if (dbError) {
      console.error('Error storing payment record:', dbError);
      // Continue anyway, as payment URL is still valid
    }

    return {
      success: true,
      payment_url: responseData.payment_url,
      invoice_id: responseData.invoice_id || invoiceId
    };

  } catch (error) {
    console.error('Error initializing payment:', error);
    return { 
      success: false, 
      error: 'Payment initialization failed' 
    };
  }
};

/**
 * Verify payment status
 */
export const verifyPayment = async (invoiceId: string): Promise<any> => {
  try {
    const uddoktaPayApiKey = 'rnDmmCuY0uiPU7rgoBGsWYbx97tJxNFpapmKwXYU';
    const verifyUrl = 'https://digitaldot.paymently.io/api/verify-payment';

    if (!uddoktaPayApiKey) {
      throw new Error('UddoktaPay API key not configured');
    }

    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'RT-UDDOKTAPAY-API-KEY': uddoktaPayApiKey,
      },
      body: JSON.stringify({
        invoice_id: invoiceId
      }),
    });

    const responseData = await response.json();

    if (!response.ok) {
      throw new Error(responseData.message || 'Payment verification failed');
    }

    // Update payment record in database
    await supabase
      .from('payment_records')
      .update({
        status: responseData.status?.toLowerCase(),
        verification_response: responseData,
        verified_at: new Date().toISOString()
      })
      .eq('invoice_id', invoiceId);

    return responseData;

  } catch (error) {
    console.error('Error verifying payment:', error);
    throw error;
  }
};

/**
 * Get payment history for a user
 */
export const getPaymentHistory = async (userId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_records')
      .select(`
        *,
        bookings:booking_id(booking_number, venue_id, venues(name)),
        coach_sessions:session_id(session_number, coach_id, coaches(user_id, users(full_name)))
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching payment history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getPaymentHistory:', error);
    return [];
  }
};

/**
 * Process refund (admin only)
 */
export const processRefund = async (
  invoiceId: string,
  refundAmount?: number,
  reason?: string
): Promise<boolean> => {
  try {
    // Note: UddoktaPay refund API implementation would go here
    // For now, we'll just update the database record
    
    const { error } = await supabase
      .from('payment_records')
      .update({
        status: 'refunded',
        refund_amount: refundAmount,
        refund_reason: reason,
        refunded_at: new Date().toISOString()
      })
      .eq('invoice_id', invoiceId);

    if (error) {
      console.error('Error processing refund:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in processRefund:', error);
    return false;
  }
};

export default {
  initializePayment,
  verifyPayment,
  getPaymentHistory,
  processRefund,
};
