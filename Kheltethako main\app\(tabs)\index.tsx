import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { MapPin, Star, Filter } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { getPopularVenues, getRecommendedCoaches, getSports } from '@/services/supabaseApi';

const SPORT_ICONS = {
  'Cricket': '🏏',
  'Football': '⚽',
  'Basketball': '🏀',
  'Badminton': '🏸',
  'Swimming': '🏊‍♂️',
  'Tennis': '🎾',
  'Table Tennis': '🏓',
  'Volleyball': '🏐',
  'Squash': '🎾',
  'Hockey': '🏑',
};

export default function HomeScreen() {
  const { user } = useAuth();
  const [venues, setVenues] = useState([]);
  const [coaches, setCoaches] = useState([]);
  const [sports, setSports] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [venueData, coachData, sportsData] = await Promise.all([
        getPopularVenues(selectedCategory),
        getRecommendedCoaches(selectedCategory),
        getSports(),
      ]);
      setVenues(venueData);
      setCoaches(coachData);
      setSports(sportsData);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const handleCategoryPress = (categoryId) => {
    if (selectedCategory === categoryId) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(categoryId);
    }
  };

  useEffect(() => {
    fetchData();
  }, [selectedCategory]);

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>
              Hello, {user?.name || 'Guest'}!
            </Text>
            <View style={styles.locationContainer}>
              <MapPin size={16} color="#6B7280" />
              <Text style={styles.locationText}>Dhaka, Bangladesh</Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => router.push('/filters')}
          >
            <Filter size={20} color="#111827" />
          </TouchableOpacity>
        </View>

        <View style={styles.bannerContainer}>
          <Image
            source={{
              uri: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
            }}
            style={styles.bannerImage}
          />
          <View style={styles.bannerOverlay}>
            <Text style={styles.bannerTitle}>Special Discount!</Text>
            <Text style={styles.bannerSubtitle}>
              Get 20% off on weekend bookings
            </Text>
            <TouchableOpacity style={styles.bannerButton}>
              <Text style={styles.bannerButtonText}>Book Now</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Sports</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesContainer}
        >
          {sports.map((sport) => (
            <TouchableOpacity
              key={sport.id}
              style={[
                styles.categoryItem,
                selectedCategory === sport.id && styles.categoryItemSelected,
              ]}
              onPress={() => handleCategoryPress(sport.id)}
            >
              <Text style={styles.categoryIcon}>
                {SPORT_ICONS[sport.name] || '🏃‍♂️'}
              </Text>
              <Text
                style={[
                  styles.categoryName,
                  selectedCategory === sport.id &&
                    styles.categoryNameSelected,
                ]}
              >
                {sport.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Popular Venues</Text>
          <TouchableOpacity onPress={() => router.push('/explore')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <ActivityIndicator
            size="large"
            color="#EF4444"
            style={styles.loader}
          />
        ) : (
          <FlatList
            data={venues}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.venuesContainer}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.venueCard}
                onPress={() => router.push(`/venue/${item.id}`)}
              >
                <Image
                  source={{ uri: item.image_url }}
                  style={styles.venueImage}
                />
                <View style={styles.venueDetails}>
                  <Text style={styles.venueName}>{item.name}</Text>
                  <View style={styles.venueLocation}>
                    <MapPin size={14} color="#6B7280" />
                    <Text style={styles.venueLocationText}>
                      {item.location}
                    </Text>
                  </View>
                  <View style={styles.venueFooter}>
                    <View style={styles.venueRating}>
                      <Star size={14} color="#F59E0B" fill="#F59E0B" />
                      <Text style={styles.venueRatingText}>
                        {item.rating} ({item.reviews_count})
                      </Text>
                    </View>
                    <Text style={styles.venuePrice}>
                      ৳{item.price_per_hour}/hr
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  No venues found. Try a different category.
                </Text>
              </View>
            }
          />
        )}

        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Top Coaches</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>

        {isLoading ? (
          <ActivityIndicator
            size="large"
            color="#EF4444"
            style={styles.loader}
          />
        ) : (
          <FlatList
            data={coaches}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.coachesContainer}
            keyExtractor={(item) => item.id.toString()}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.coachCard}
                onPress={() => router.push(`/coach/${item.id}`)}
              >
                <Image
                  source={{ uri: item.image_url }}
                  style={styles.coachImage}
                />
                <View style={styles.coachDetails}>
                  <Text style={styles.coachName}>{item.name}</Text>
                  <Text style={styles.coachSpecialty}>{item.specialty}</Text>
                  <View style={styles.coachFooter}>
                    <View style={styles.coachRating}>
                      <Star size={14} color="#F59E0B" fill="#F59E0B" />
                      <Text style={styles.coachRatingText}>
                        {item.rating} ({item.reviews_count})
                      </Text>
                    </View>
                    <Text style={styles.coachPrice}>
                      ৳{item.price_per_session}/session
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  No coaches found. Try a different category.
                </Text>
              </View>
            }
          />
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  greeting: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 2,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  filterButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bannerContainer: {
    margin: 16,
    height: 140,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  bannerImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  bannerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(239, 68, 68, 0.7)',
    padding: 16,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
    marginBottom: 6,
  },
  bannerSubtitle: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  bannerButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  bannerButtonText: {
    color: '#EF4444',
    fontSize: 13,
    fontFamily: 'Inter-SemiBold',
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    paddingHorizontal: 20,
    marginBottom: 12,
    marginTop: 8,
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  categoryItem: {
    marginHorizontal: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    flexDirection: 'row',
  },
  categoryItemSelected: {
    backgroundColor: '#EF4444',
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  categoryName: {
    fontFamily: 'Inter-Medium',
    color: '#374151',
    fontSize: 14,
  },
  categoryNameSelected: {
    color: '#FFFFFF',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 12,
    marginTop: 8,
  },
  seeAllText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
  },
  loader: {
    marginVertical: 20,
  },
  venuesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  venueCard: {
    width: 280,
    marginRight: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  venueImage: {
    width: '100%',
    height: 150,
  },
  venueDetails: {
    padding: 12,
  },
  venueName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 6,
  },
  venueLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  venueLocationText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 4,
  },
  venueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  venueRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  venueRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 4,
  },
  venuePrice: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#EF4444',
  },
  coachesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  coachCard: {
    width: 220,
    marginRight: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  coachImage: {
    width: '100%',
    height: 180,
  },
  coachDetails: {
    padding: 12,
  },
  coachName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  coachSpecialty: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 8,
  },
  coachFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  coachRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coachRatingText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginLeft: 4,
  },
  coachPrice: {
    fontSize: 13,
    fontFamily: 'Inter-Bold',
    color: '#EF4444',
  },
  emptyState: {
    width: 280,
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  emptyStateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});