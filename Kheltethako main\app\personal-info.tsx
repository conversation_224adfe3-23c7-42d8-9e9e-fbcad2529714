import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, User, Mail, Phone, MapPin, Calendar, Edit3 } from 'lucide-react-native';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';

interface PersonalInfo {
  full_name: string;
  email: string;
  phone: string;
  location: string;
  date_of_birth: string;
  gender: string;
  emergency_contact: string;
  emergency_phone: string;
}

export default function PersonalInfoScreen() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    full_name: '',
    email: '',
    phone: '',
    location: '',
    date_of_birth: '',
    gender: '',
    emergency_contact: '',
    emergency_phone: '',
  });

  useEffect(() => {
    fetchPersonalInfo();
  }, []);

  const fetchPersonalInfo = async () => {
    try {
      setIsLoading(true);
      if (!user?.id) return;

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      setPersonalInfo({
        full_name: data.full_name || '',
        email: data.email || '',
        phone: data.phone || '',
        location: data.location || '',
        date_of_birth: data.date_of_birth || '',
        gender: data.gender || '',
        emergency_contact: data.emergency_contact || '',
        emergency_phone: data.emergency_phone || '',
      });
    } catch (error) {
      console.error('Error fetching personal info:', error);
      Alert.alert('Error', 'Failed to load personal information');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      if (!user?.id) return;

      const { error } = await supabase
        .from('users')
        .update({
          full_name: personalInfo.full_name,
          phone: personalInfo.phone,
          location: personalInfo.location,
          date_of_birth: personalInfo.date_of_birth,
          gender: personalInfo.gender,
          emergency_contact: personalInfo.emergency_contact,
          emergency_phone: personalInfo.emergency_phone,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) throw error;

      Alert.alert('Success', 'Personal information updated successfully');
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating personal info:', error);
      Alert.alert('Error', 'Failed to update personal information');
    } finally {
      setIsSaving(false);
    }
  };

  const updateField = (field: keyof PersonalInfo, value: string) => {
    setPersonalInfo(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const InfoField = ({ 
    icon, 
    label, 
    value, 
    field, 
    editable = true,
    keyboardType = 'default' as any
  }: {
    icon: React.ReactNode;
    label: string;
    value: string;
    field: keyof PersonalInfo;
    editable?: boolean;
    keyboardType?: any;
  }) => (
    <View style={styles.infoField}>
      <View style={styles.fieldHeader}>
        <View style={styles.fieldLabelContainer}>
          {icon}
          <Text style={styles.fieldLabel}>{label}</Text>
        </View>
        {!editable && (
          <View style={styles.readOnlyBadge}>
            <Text style={styles.readOnlyText}>Read Only</Text>
          </View>
        )}
      </View>
      {isEditing && editable ? (
        <TextInput
          style={styles.fieldInput}
          value={value}
          onChangeText={(text) => updateField(field, text)}
          placeholder={`Enter ${label.toLowerCase()}`}
          placeholderTextColor="#9CA3AF"
          keyboardType={keyboardType}
        />
      ) : (
        <Text style={[styles.fieldValue, !value && styles.fieldValueEmpty]}>
          {value || `No ${label.toLowerCase()} provided`}
        </Text>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={20} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.title}>Personal Information</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#EF4444" />
          <Text style={styles.loadingText}>Loading personal information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={20} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.title}>Personal Information</Text>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => {
            if (isEditing) {
              handleSave();
            } else {
              setIsEditing(true);
            }
          }}
          disabled={isSaving}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#EF4444" />
          ) : (
            <>
              <Edit3 size={16} color="#EF4444" />
              <Text style={styles.editButtonText}>
                {isEditing ? 'Save' : 'Edit'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>
          
          <InfoField
            icon={<User size={18} color="#6B7280" />}
            label="Full Name"
            value={personalInfo.full_name}
            field="full_name"
          />
          
          <InfoField
            icon={<Mail size={18} color="#6B7280" />}
            label="Email Address"
            value={personalInfo.email}
            field="email"
            editable={false}
          />
          
          <InfoField
            icon={<Phone size={18} color="#6B7280" />}
            label="Phone Number"
            value={personalInfo.phone}
            field="phone"
            keyboardType="phone-pad"
          />
          
          <InfoField
            icon={<MapPin size={18} color="#6B7280" />}
            label="Location"
            value={personalInfo.location}
            field="location"
          />
          
          <InfoField
            icon={<Calendar size={18} color="#6B7280" />}
            label="Date of Birth"
            value={personalInfo.date_of_birth}
            field="date_of_birth"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Emergency Contact</Text>
          
          <InfoField
            icon={<User size={18} color="#6B7280" />}
            label="Emergency Contact Name"
            value={personalInfo.emergency_contact}
            field="emergency_contact"
          />
          
          <InfoField
            icon={<Phone size={18} color="#6B7280" />}
            label="Emergency Contact Phone"
            value={personalInfo.emergency_phone}
            field="emergency_phone"
            keyboardType="phone-pad"
          />
        </View>

        {isEditing && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setIsEditing(false);
                fetchPersonalInfo(); // Reset to original values
              }}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  editButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#EF4444',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 12,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 16,
  },
  infoField: {
    marginBottom: 20,
  },
  fieldHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fieldLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldLabel: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginLeft: 8,
  },
  readOnlyBadge: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  readOnlyText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  fieldValue: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    paddingVertical: 8,
  },
  fieldValueEmpty: {
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
  fieldInput: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: '#F9FAFB',
  },
  actionButtons: {
    padding: 16,
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
});
